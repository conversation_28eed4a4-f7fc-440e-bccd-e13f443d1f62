<?php

/**
 * 音频流客户端 - 直接获取音频流数据
 * 
 * 这个客户端专门用于从音频代理服务获取解密后的音频流
 * 支持完整文件和范围请求
 */
class AudioStreamClient {
    private $baseUrl;
    private $apiKey;
    private $timeout;

    public function __construct($baseUrl, $apiKey, $timeout = 30) {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->apiKey = $apiKey;
        $this->timeout = $timeout;
    }

    /**
     * 获取完整音频流
     * 
     * @param string $fileId 文件ID
     * @param string $userId 用户ID
     * @param string $authToken 认证令牌
     * @return resource 音频流资源
     */
    public function getAudioStream($fileId, $userId, $authToken) {
        $request = [
            'file_id' => $fileId,
            'user_id' => $userId,
            'auth_token' => $authToken
        ];

        return $this->makeStreamRequest('/stream', $request);
    }

    /**
     * 获取音频流的指定范围
     * 
     * @param string $fileId 文件ID
     * @param string $userId 用户ID
     * @param string $authToken 认证令牌
     * @param int $start 起始字节
     * @param int|null $end 结束字节（null表示到文件末尾）
     * @return resource 音频流资源
     */
    public function getAudioStreamRange($fileId, $userId, $authToken, $start, $end = null) {
        $range = "bytes={$start}-";
        if ($end !== null) {
            $range .= $end;
        }

        $request = [
            'file_id' => $fileId,
            'user_id' => $userId,
            'auth_token' => $authToken,
            'range' => $range
        ];

        return $this->makeStreamRequest('/stream', $request);
    }

    /**
     * 将音频流输出到浏览器
     * 
     * @param string $fileId 文件ID
     * @param string $userId 用户ID
     * @param string $authToken 认证令牌
     * @param array $headers 额外的HTTP头
     */
    public function streamToBrowser($fileId, $userId, $authToken, $headers = []) {
        // 处理 Range 请求
        $range = null;
        if (isset($_SERVER['HTTP_RANGE'])) {
            $range = $_SERVER['HTTP_RANGE'];
        }

        try {
            if ($range) {
                // 解析 Range 头
                if (preg_match('/bytes=(\d+)-(\d*)/', $range, $matches)) {
                    $start = (int)$matches[1];
                    $end = $matches[2] !== '' ? (int)$matches[2] : null;
                    
                    $stream = $this->getAudioStreamRange($fileId, $userId, $authToken, $start, $end);
                    
                    // 设置 206 Partial Content 响应
                    http_response_code(206);
                    header('Accept-Ranges: bytes');
                    
                    if ($end !== null) {
                        header("Content-Range: bytes {$start}-{$end}/*");
                        header('Content-Length: ' . ($end - $start + 1));
                    } else {
                        header("Content-Range: bytes {$start}-*/*");
                    }
                }
            } else {
                $stream = $this->getAudioStream($fileId, $userId, $authToken);
                http_response_code(200);
            }

            // 设置音频相关的头
            header('Content-Type: audio/mpeg');
            header('Accept-Ranges: bytes');
            header('Cache-Control: no-cache');
            
            // 设置额外的头
            foreach ($headers as $name => $value) {
                header("{$name}: {$value}");
            }

            // 输出音频流
            $this->outputStream($stream);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()]);
        }
    }

    /**
     * 保存音频流到文件
     * 
     * @param string $fileId 文件ID
     * @param string $userId 用户ID
     * @param string $authToken 认证令牌
     * @param string $outputPath 输出文件路径
     * @return bool 是否成功
     */
    public function saveAudioToFile($fileId, $userId, $authToken, $outputPath) {
        try {
            $stream = $this->getAudioStream($fileId, $userId, $authToken);
            $outputFile = fopen($outputPath, 'wb');
            
            if (!$outputFile) {
                throw new Exception("无法创建输出文件: {$outputPath}");
            }

            while (!feof($stream)) {
                $chunk = fread($stream, 8192);
                fwrite($outputFile, $chunk);
            }

            fclose($stream);
            fclose($outputFile);
            
            return true;
        } catch (Exception $e) {
            error_log("保存音频文件失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 生成认证令牌
     * 
     * @param string $fileId 文件ID
     * @param string $userId 用户ID
     * @param int $expiresIn 过期时间（秒）
     * @return string 认证令牌
     */
    public function generateAuthToken($fileId, $userId, $expiresIn = 3600) {
        $request = [
            'file_id' => $fileId,
            'user_id' => $userId,
            'expires_in' => $expiresIn
        ];

        $response = $this->makeJsonRequest('/auth/token', $request);
        return $response['token'] ?? null;
    }

    /**
     * 检查服务健康状态
     * 
     * @return bool 服务是否健康
     */
    public function healthCheck() {
        try {
            $response = file_get_contents($this->baseUrl . '/health');
            return $response !== false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 发送流请求
     */
    private function makeStreamRequest($endpoint, $data) {
        $url = $this->baseUrl . $endpoint;
        
        $postData = json_encode($data);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'Authorization: Bearer ' . $this->apiKey,
                    'Content-Length: ' . strlen($postData)
                ],
                'content' => $postData,
                'timeout' => $this->timeout
            ]
        ]);

        $stream = fopen($url, 'r', false, $context);
        
        if (!$stream) {
            throw new Exception('无法连接到音频服务');
        }

        return $stream;
    }

    /**
     * 发送 JSON 请求
     */
    private function makeJsonRequest($endpoint, $data) {
        $url = $this->baseUrl . $endpoint;
        
        $postData = json_encode($data);
        
        $context = stream_context_create([
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'Authorization: Bearer ' . $this->apiKey,
                    'Content-Length: ' . strlen($postData)
                ],
                'content' => $postData,
                'timeout' => $this->timeout
            ]
        ]);

        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new Exception('请求失败');
        }

        return json_decode($response, true);
    }

    /**
     * 输出流到浏览器
     */
    private function outputStream($stream) {
        // 禁用输出缓冲
        if (ob_get_level()) {
            ob_end_clean();
        }

        // 分块输出
        while (!feof($stream)) {
            $chunk = fread($stream, 8192);
            echo $chunk;
            flush();
        }

        fclose($stream);
    }
}

// 使用示例
/*
// 1. 基础使用 - 直接输出到浏览器
$client = new AudioStreamClient('http://localhost:8080', 'your-api-key');

// 生成认证令牌
$authToken = $client->generateAuthToken('song123', 'user456', 3600);

// 直接流式输出到浏览器（支持 Range 请求）
$client->streamToBrowser('song123', 'user456', $authToken);

// 2. 高级使用 - 获取流进行处理
$stream = $client->getAudioStream('song123', 'user456', $authToken);

// 读取前 1MB 数据
$data = fread($stream, 1024 * 1024);
fclose($stream);

// 3. 保存到文件
$client->saveAudioToFile('song123', 'user456', $authToken, '/tmp/audio.mp3');

// 4. 范围请求
$stream = $client->getAudioStreamRange('song123', 'user456', $authToken, 0, 1048576);
*/
