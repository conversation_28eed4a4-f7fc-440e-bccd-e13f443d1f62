<?php

require_once 'AudioStreamClient.php';

/**
 * 音频流服务使用示例
 */

// 配置
$audioServiceUrl = 'http://localhost:8080';
$apiKey = 'your-api-key';

// 创建客户端
$client = new AudioStreamClient($audioServiceUrl, $apiKey);

// 示例1: 音频播放页面
function serveAudioForPlayer($fileId, $userId) {
    global $client;
    
    try {
        // 生成认证令牌
        $authToken = $client->generateAuthToken($fileId, $userId, 3600);
        
        if (!$authToken) {
            http_response_code(401);
            echo json_encode(['error' => '认证失败']);
            return;
        }

        // 直接流式输出音频（自动处理 Range 请求）
        $client->streamToBrowser($fileId, $userId, $authToken, [
            'X-Audio-Source' => 'Music Import System',
            'X-User-ID' => $userId
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

// 示例2: 音频下载
function downloadAudio($fileId, $userId, $filename) {
    global $client;
    
    try {
        $authToken = $client->generateAuthToken($fileId, $userId, 3600);
        
        // 设置下载头
        header('Content-Type: audio/mpeg');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        $client->streamToBrowser($fileId, $userId, $authToken);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

// 示例3: 音频预览（前30秒）
function previewAudio($fileId, $userId) {
    global $client;
    
    try {
        $authToken = $client->generateAuthToken($fileId, $userId, 3600);
        
        // 获取前 1MB 作为预览（大约30秒的MP3）
        $stream = $client->getAudioStreamRange($fileId, $userId, $authToken, 0, 1048576);
        
        header('Content-Type: audio/mpeg');
        header('Content-Length: 1048576');
        
        while (!feof($stream)) {
            echo fread($stream, 8192);
            flush();
        }
        
        fclose($stream);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

// 示例4: 音频信息获取
function getAudioInfo($fileId, $userId) {
    global $client;
    
    try {
        $authToken = $client->generateAuthToken($fileId, $userId, 3600);
        
        // 获取前几个字节来分析文件信息
        $stream = $client->getAudioStreamRange($fileId, $userId, $authToken, 0, 1024);
        $header = fread($stream, 1024);
        fclose($stream);
        
        // 简单的 MP3 头部分析
        $info = [
            'file_id' => $fileId,
            'format' => 'MP3',
            'has_id3' => substr($header, 0, 3) === 'ID3',
            'header_size' => strlen($header)
        ];
        
        header('Content-Type: application/json');
        echo json_encode($info);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => $e->getMessage()]);
    }
}

// 示例5: 批量音频处理
function processBatchAudio($fileIds, $userId) {
    global $client;
    
    $results = [];
    
    foreach ($fileIds as $fileId) {
        try {
            $authToken = $client->generateAuthToken($fileId, $userId, 3600);
            
            // 保存到临时文件进行处理
            $tempFile = tempnam(sys_get_temp_dir(), 'audio_');
            
            if ($client->saveAudioToFile($fileId, $userId, $authToken, $tempFile)) {
                // 这里可以进行音频处理，如格式转换、音质分析等
                $fileSize = filesize($tempFile);
                
                $results[$fileId] = [
                    'status' => 'success',
                    'size' => $fileSize,
                    'temp_file' => $tempFile
                ];
            } else {
                $results[$fileId] = [
                    'status' => 'failed',
                    'error' => '下载失败'
                ];
            }
            
        } catch (Exception $e) {
            $results[$fileId] = [
                'status' => 'error',
                'error' => $e->getMessage()
            ];
        }
    }
    
    header('Content-Type: application/json');
    echo json_encode($results);
}

// 路由处理
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $action = $_GET['action'] ?? '';
    $fileId = $_GET['file_id'] ?? '';
    $userId = $_GET['user_id'] ?? '';
    
    switch ($action) {
        case 'play':
            serveAudioForPlayer($fileId, $userId);
            break;
            
        case 'download':
            $filename = $_GET['filename'] ?? 'audio.mp3';
            downloadAudio($fileId, $userId, $filename);
            break;
            
        case 'preview':
            previewAudio($fileId, $userId);
            break;
            
        case 'info':
            getAudioInfo($fileId, $userId);
            break;
            
        case 'health':
            $healthy = $client->healthCheck();
            header('Content-Type: application/json');
            echo json_encode(['healthy' => $healthy]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => '无效的操作']);
    }
    
} elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    switch ($action) {
        case 'batch_process':
            $fileIds = $input['file_ids'] ?? [];
            $userId = $input['user_id'] ?? '';
            processBatchAudio($fileIds, $userId);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => '无效的操作']);
    }
}

/*
使用示例 URL:

1. 播放音频:
   GET /example.php?action=play&file_id=song123&user_id=user456

2. 下载音频:
   GET /example.php?action=download&file_id=song123&user_id=user456&filename=my_song.mp3

3. 预览音频:
   GET /example.php?action=preview&file_id=song123&user_id=user456

4. 获取音频信息:
   GET /example.php?action=info&file_id=song123&user_id=user456

5. 健康检查:
   GET /example.php?action=health

6. 批量处理:
   POST /example.php
   Content-Type: application/json
   {
     "action": "batch_process",
     "file_ids": ["song123", "song456"],
     "user_id": "user456"
   }
*/
