# 🎵 音乐导入工具 - Windows 测试包

## 📦 包含文件

```
windows_release/
├── music_import.exe          # 主程序
├── start_server.bat          # 服务器启动脚本
├── start_client.bat          # 客户端启动脚本
├── config/                   # 配置文件目录
│   ├── server.json          # 服务器配置
│   ├── sony.json            # Sony 客户端配置
│   └── ...                  # 其他配置文件
├── test_scripts/            # 测试脚本
├── data/sony/               # Sony 数据目录 (放置 DDEX XML 文件)
├── logs/                    # 日志目录
└── uploads/                 # 上传文件目录
```

## 🔑 重要信息

**AUDIO_MASTER_KEY**: `26623413dfda3e35e41372cf39978d7f34bb1c3b6bf4013dc1eb9a7a0fd4c367`

此密钥已预设在启动脚本中，用于 V1 加密模式。

## 🚀 快速开始

### 1. 启动 NAS 服务器
双击运行 `start_server.bat`

服务器将在 `0.0.0.0:50051` 监听 gRPC 连接。

### 2. 准备测试数据
将 Sony DDEX XML 文件放入 `data/sony/` 目录

### 3. 启动客户端
双击运行 `start_client.bat`

客户端将自动扫描并处理 XML 文件。

## ⚙️ 配置说明

### 当前加密配置 (V1 模式)
- **加密算法**: AES-256-GCM
- **安全级别**: 军用级
- **文件扩展名**: `.encrypted`
- **需要密钥**: 是

### 数据库配置
- **主机**: *************:3336
- **数据库**: avs
- **用户**: avs

## 🔧 手动运行命令

如果需要手动运行，请先设置环境变量：

```cmd
set AUDIO_MASTER_KEY=26623413dfda3e35e41372cf39978d7f34bb1c3b6bf4013dc1eb9a7a0fd4c367

REM 启动服务器
music_import.exe --mode server --config config/server.json

REM 启动客户端
music_import.exe --mode client --config config/sony.json
```

## 📊 处理流程

1. **扫描 XML 文件** → 解析 DDEX 元数据
2. **智能数据对比** → 检测重复和变更
3. **加密文件上传** → 安全传输到 NAS
4. **数据库更新** → 维护音乐库信息
5. **生成处理报告** → 查看 logs/ 目录

## 🐛 故障排除

### 常见问题

1. **连接数据库失败**
   - 检查网络连接
   - 确认数据库服务器可访问

2. **gRPC 连接失败**
   - 确保服务器已启动
   - 检查防火墙设置

3. **加密错误**
   - 确认 AUDIO_MASTER_KEY 已正确设置
   - 检查密钥长度 (必须是 64 个十六进制字符)

### 日志查看
处理日志保存在 `logs/` 目录，按日期分类。

## 📞 技术支持

如有问题，请查看日志文件或联系开发团队。
