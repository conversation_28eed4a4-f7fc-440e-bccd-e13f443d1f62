#!/bin/bash

# 轻量级KMS快速启动脚本
# 使用方法: ./scripts/quick_start_kms.sh

set -e

echo "🚀 轻量级KMS快速部署脚本"
echo "=========================="

# 检查Rust环境
if ! command -v cargo &> /dev/null; then
    echo "❌ 错误: 未找到Rust环境，请先安装Rust"
    echo "   访问: https://rustup.rs/"
    exit 1
fi

# 检查openssl
if ! command -v openssl &> /dev/null; then
    echo "❌ 错误: 未找到openssl，请安装openssl"
    exit 1
fi

echo "✅ 环境检查通过"

# 1. 编译项目
echo "🔨 编译KMS服务器..."
cargo build --release --bin kms_server --bin key_manager

echo "✅ 编译完成"

# 2. 创建目录
echo "📁 创建配置目录..."
mkdir -p config
mkdir -p logs

# 3. 生成Master Key
echo "🔑 生成Master Key..."
MASTER_KEY=$(openssl rand -hex 32)
echo "AUDIO_MASTER_KEY=$MASTER_KEY" > .env

echo "✅ Master Key已生成并保存到 .env 文件"

# 4. 创建配置文件
echo "📝 创建配置文件..."

# KMS配置
cat > config/kms-config.json << EOF
{
  "port": 8080,
  "key_manager_config": "./config/key-manager.json",
  "cache_ttl": 3600,
  "api_token": "dev_token_$(openssl rand -hex 8)",
  "enable_audit": true
}
EOF

# 密钥管理器配置
cat > config/key-manager.json << EOF
{
  "rotation_months": 6,
  "max_keys": 4,
  "key_store_path": "./config/keys.json",
  "auto_cleanup": true,
  "master_key_env": "AUDIO_MASTER_KEY"
}
EOF

echo "✅ 配置文件已创建"

# 5. 初始化密钥管理器
echo "🔧 初始化密钥管理器..."
source .env
./target/release/key_manager init -c ./config/key-manager.json

echo "✅ 密钥管理器初始化完成"

# 6. 创建启动脚本
echo "📜 创建启动脚本..."
cat > start_kms.sh << 'EOF'
#!/bin/bash
set -e

echo "🚀 启动轻量级KMS服务器..."

# 加载环境变量
if [ -f .env ]; then
    source .env
    echo "✅ 已加载环境变量"
else
    echo "❌ 错误: 未找到 .env 文件"
    exit 1
fi

# 检查Master Key
if [ -z "$AUDIO_MASTER_KEY" ]; then
    echo "❌ 错误: AUDIO_MASTER_KEY 未设置"
    exit 1
fi

# 启动服务器
echo "🌟 KMS服务器启动中..."
echo "📡 API地址: http://localhost:8080"
echo "📚 API文档: http://localhost:8080/api/help" 
echo "🛑 停止服务: Ctrl+C"
echo ""

./target/release/kms_server -c ./config/kms-config.json
EOF

chmod +x start_kms.sh

# 7. 创建测试脚本
echo "🧪 创建测试脚本..."
cat > test_kms.sh << 'EOF'
#!/bin/bash

echo "🧪 KMS功能测试"
echo "=============="

KMS_URL="http://localhost:8080"

# 1. 健康检查
echo "1. 健康检查..."
if curl -s "$KMS_URL/api/v1/status" | grep -q "healthy"; then
    echo "✅ KMS服务正常运行"
else
    echo "❌ KMS服务异常"
    exit 1
fi

# 2. 创建测试文件
echo "2. 创建测试文件..."
echo "Hello KMS Test" > test_input.txt

# 3. 测试文件加密
echo "3. 测试文件加密..."
curl -s -X POST "$KMS_URL/api/v1/encrypt/file" \
  -H "Content-Type: application/json" \
  -d '{
    "input_path": "./test_input.txt",
    "output_path": "./test_encrypted.bin",
    "file_id": "test_file_123"
  }' | grep -q '"success":true' && echo "✅ 加密成功" || echo "❌ 加密失败"

# 4. 测试文件解密
echo "4. 测试文件解密..."
curl -s -X POST "$KMS_URL/api/v1/decrypt/file" \
  -H "Content-Type: application/json" \
  -d '{
    "encrypted_path": "./test_encrypted.bin",
    "output_path": "./test_decrypted.txt"
  }' | grep -q '"success":true' && echo "✅ 解密成功" || echo "❌ 解密失败"

# 5. 验证内容
echo "5. 验证解密内容..."
if diff test_input.txt test_decrypted.txt > /dev/null; then
    echo "✅ 内容验证成功"
else
    echo "❌ 内容验证失败"
fi

# 清理测试文件
rm -f test_input.txt test_encrypted.bin test_decrypted.txt

echo ""
echo "🎉 KMS功能测试完成！"
EOF

chmod +x test_kms.sh

# 8. 创建systemd服务文件（可选）
echo "🔧 创建systemd服务文件..."
cat > kms.service << EOF
[Unit]
Description=Lightweight KMS Server
After=network.target

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=$(pwd)
Environment=AUDIO_MASTER_KEY=$MASTER_KEY
ExecStart=$(pwd)/target/release/kms_server -c $(pwd)/config/kms-config.json
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

echo "✅ systemd服务文件已创建: kms.service"

# 完成提示
echo ""
echo "🎉 KMS部署完成！"
echo "================"
echo ""
echo "📋 下一步操作:"
echo "1. 启动KMS服务:"
echo "   ./start_kms.sh"
echo ""
echo "2. 测试KMS功能:"
echo "   ./test_kms.sh"
echo ""
echo "3. 查看API文档:"
echo "   http://localhost:8080/api/help"
echo ""
echo "4. 安装为系统服务 (可选):"
echo "   sudo cp kms.service /etc/systemd/system/"
echo "   sudo systemctl enable kms"
echo "   sudo systemctl start kms"
echo ""
echo "📁 重要文件:"
echo "   - Master Key: .env"
echo "   - KMS配置: config/kms-config.json"
echo "   - 密钥状态: config/keys.json"
echo ""
echo "⚠️  注意: .env文件包含敏感信息，请妥善保管！" 