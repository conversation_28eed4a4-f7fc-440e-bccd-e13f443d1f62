syntax = "proto3";

package file_transfer;

service FileTransferService {
    rpc UploadFile(stream FileChunk) returns (UploadResponse);
    rpc CheckUploadStatus(StatusRequest) returns (StatusResponse);
}

message FileChunk {
    string file_id = 1;
    uint64 offset = 2;
    bytes data = 3;
    uint64 total_size = 4;
    bool is_last_chunk = 5;
    string file_name = 6;
}

message UploadResponse {
    bool success = 1;
    string message = 2;
    uint64 bytes_received = 3;
}

message StatusRequest {
    string file_id = 1;
    string file_hash = 2;  // 客户端提供的文件校验和
    uint64 file_size = 3;  // 客户端提供的文件大小
}

message StatusResponse {
    uint64 bytes_uploaded = 1;
    bool exists = 2;
    bool is_complete = 3;  // 文件是否完整
    string server_hash = 4; // 服务器端的文件校验和
}