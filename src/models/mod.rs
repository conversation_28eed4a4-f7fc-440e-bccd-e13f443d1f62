use serde::{Deserialize, Serialize};
use std::collections::HashMap;

#[derive(Debug, <PERSON><PERSON>, Serial<PERSON>, Deserialize)]
pub struct Album {
    pub id: Option<i64>,
    pub title: String,
    pub formal_title: Title,
    pub grid: String,
    pub icpn: String,
    pub release_type: String,
    pub release_date: String,
    pub duration: i64,
    pub copyright: String,
    pub cover_path: String,
    pub tracks: Vec<Track>,
    pub success: bool,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Track {
    pub id: Option<i64>,
    pub album_id: Option<i64>,
    pub title: String,
    pub formal_title: Title,
    pub isrc: String,
    pub duration: i64,
    pub track_number: i32,
    pub artists: Vec<Artist>,
    pub file_path: String,
    pub md5: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Title {
    pub name: String,
    pub language: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Artist {
    pub id: Option<i64>,
    pub name: String,
    pub role: String,
}

#[allow(dead_code)]
impl Album {
    pub fn new(title: String, grid: String, icpn: String) -> Self {
        Album {
            id: None,
            title,
            formal_title: Title { name: String::new(), language: String::new() },
            grid,
            icpn,
            release_type: String::new(),
            release_date: String::new(),
            duration: 0,
            copyright: String::new(),
            cover_path: String::new(),
            tracks: Vec::new(),
            success: true,
        }
    }
    
    pub fn add_track(&mut self, track: Track) {
        self.tracks.push(track);
    }
    
    pub fn to_db_params(&self) -> HashMap<String, String> {
        let mut params = HashMap::new();
        
        params.insert("title".to_string(), self.title.clone());
        params.insert("grid".to_string(), self.grid.clone());
        params.insert("icpn".to_string(), self.icpn.clone());
        params.insert("release_type".to_string(), self.release_type.clone());
        params.insert("release_date".to_string(), self.release_date.clone());
        params.insert("duration".to_string(), self.duration.to_string());
        params.insert("copyright".to_string(), self.copyright.clone());
        params.insert("cover_path".to_string(), self.cover_path.clone());
        
        params
    }
}

#[allow(dead_code)]
impl Track {
    pub fn new(title: String, isrc: String, duration: i64, track_number: i32) -> Self {
        Track {
            id: None,
            album_id: None,
            title,
            formal_title: Title { name: String::new(), language: String::new() },
            isrc,
            duration,
            track_number,
            artists: Vec::new(),
            file_path: String::new(),
            md5: String::new(),
        }
    }
    
    pub fn add_artist(&mut self, artist: Artist) {
        self.artists.push(artist);
    }
    
    pub fn to_db_params(&self) -> HashMap<String, String> {
        let mut params = HashMap::new();
        
        if let Some(album_id) = self.album_id {
            params.insert("album_id".to_string(), album_id.to_string());
        }
        
        params.insert("title".to_string(), self.title.clone());
        params.insert("isrc".to_string(), self.isrc.clone());
        params.insert("duration".to_string(), self.duration.to_string());
        params.insert("track_number".to_string(), self.track_number.to_string());
        params.insert("file_path".to_string(), self.file_path.clone());
        params.insert("md5".to_string(), self.md5.clone());
        
        params
    }
}

#[allow(dead_code)]
impl Artist {
    pub fn new(name: String, role: String) -> Self {
        Artist {
            id: None,
            name,
            role,
        }
    }
}