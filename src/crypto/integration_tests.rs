//! 加密系统集成测试
//! 
//! 验证老版和新版加密的正确性和兼容性

#[cfg(test)]
mod tests {
    use super::super::*;
    use crate::config::EncryptionConfig;
    use std::fs;
    use tempfile::tempdir;
    use tokio;

    /// 创建测试用的配置
    fn create_test_config(version: &str, method: &str) -> EncryptionConfig {
        EncryptionConfig {
            version: version.to_string(),
            method: method.to_string(),
        }
    }

    /// 创建测试数据
    fn create_test_data() -> Vec<u8> {
        b"This is test audio data for encryption compatibility testing. \
          It contains various bytes including: \x00\x01\x02\xFF\xFE\xFD. \
          This should be enough data to test the encryption properly."
            .to_vec()
    }

    #[tokio::test]
    async fn test_legacy_encryption_roundtrip() {
        let config = create_test_config("legacy", "bitwise_not");
        let encryptor = EncryptionFactory::create_encryptor(&config).unwrap();
        
        let temp_dir = tempdir().unwrap();
        let input_path = temp_dir.path().join("test.mp3");
        let encrypted_path = temp_dir.path().join("test.bin");
        let output_path = temp_dir.path().join("test_decrypted.mp3");
        
        let test_data = create_test_data();
        fs::write(&input_path, &test_data).unwrap();
        
        // 加密
        encryptor.encrypt_file(
            input_path.to_str().unwrap(),
            encrypted_path.to_str().unwrap(),
            "test_file_legacy"
        ).await.unwrap();
        
        // 验证加密文件存在且大小相同（老版加密不改变文件大小）
        assert!(encrypted_path.exists());
        let encrypted_data = fs::read(&encrypted_path).unwrap();
        assert_eq!(encrypted_data.len(), test_data.len());
        
        // 验证数据确实被加密了（不等于原始数据）
        assert_ne!(encrypted_data, test_data);
        
        // 解密
        encryptor.decrypt_file(
            encrypted_path.to_str().unwrap(),
            output_path.to_str().unwrap()
        ).await.unwrap();
        
        // 验证解密结果
        let decrypted_data = fs::read(&output_path).unwrap();
        assert_eq!(decrypted_data, test_data);
    }

    #[tokio::test]
    async fn test_legacy_encryption_range_access() {
        let config = create_test_config("legacy", "bitwise_not");
        let encryptor = EncryptionFactory::create_encryptor(&config).unwrap();
        
        let temp_dir = tempdir().unwrap();
        let input_path = temp_dir.path().join("test.mp3");
        let encrypted_path = temp_dir.path().join("test.bin");
        
        let test_data = create_test_data();
        fs::write(&input_path, &test_data).unwrap();
        
        // 加密
        encryptor.encrypt_file(
            input_path.to_str().unwrap(),
            encrypted_path.to_str().unwrap(),
            "test_file_range"
        ).await.unwrap();
        
        // 测试范围解密
        let start = 10;
        let length = 20;
        let range_data = encryptor.decrypt_range(
            encrypted_path.to_str().unwrap(),
            start,
            length
        ).await.unwrap();
        
        // 验证范围解密结果
        assert_eq!(range_data.len(), length as usize);
        assert_eq!(range_data, &test_data[start as usize..(start + length) as usize]);
    }

    #[tokio::test]
    async fn test_encryption_factory_validation() {
        // 测试有效的老版配置
        let valid_legacy_config = create_test_config("legacy", "bitwise_not");
        assert!(EncryptionFactory::validate_config(&valid_legacy_config).is_ok());

        // 测试无效的老版配置
        let invalid_legacy_config = create_test_config("legacy", "aes_gcm");
        assert!(EncryptionFactory::validate_config(&invalid_legacy_config).is_err());

        // 测试有效的新版配置
        let valid_v1_config = create_test_config("v1", "aes_gcm");
        assert!(EncryptionFactory::validate_config(&valid_v1_config).is_ok());

        // 测试无效的新版配置
        let invalid_v1_config = create_test_config("v1", "bitwise_not");
        assert!(EncryptionFactory::validate_config(&invalid_v1_config).is_err());
    }

    #[tokio::test]
    async fn test_encryption_factory_creation() {
        // 测试创建老版加密器
        let legacy_encryptor = EncryptionFactory::create_encryptor_by_version("legacy");
        assert!(legacy_encryptor.is_ok());
        
        // 测试不支持的版本
        let invalid_encryptor = EncryptionFactory::create_encryptor_by_version("invalid");
        assert!(invalid_encryptor.is_err());
    }

    #[tokio::test]
    async fn test_bitwise_not_properties() {
        // 测试按位取反的数学性质
        let test_bytes = vec![0x00, 0x01, 0x55, 0xAA, 0xFF, 0x80, 0x7F];
        
        for &byte in &test_bytes {
            let encrypted = !byte;
            let decrypted = !encrypted;
            assert_eq!(decrypted, byte, "按位取反双重操作应该回到原值");
        }
    }

    #[tokio::test]
    async fn test_file_info_extraction() {
        let config = create_test_config("legacy", "bitwise_not");
        let encryptor = EncryptionFactory::create_encryptor(&config).unwrap();
        
        let temp_dir = tempdir().unwrap();
        let input_path = temp_dir.path().join("test.mp3");
        let encrypted_path = temp_dir.path().join("test.bin");
        
        let test_data = create_test_data();
        fs::write(&input_path, &test_data).unwrap();
        
        // 加密
        encryptor.encrypt_file(
            input_path.to_str().unwrap(),
            encrypted_path.to_str().unwrap(),
            "test_file_info"
        ).await.unwrap();
        
        // 获取文件信息
        let file_info = encryptor.get_file_info(
            encrypted_path.to_str().unwrap()
        ).await.unwrap();
        
        assert!(file_info.is_some());
        let info = file_info.unwrap();
        assert_eq!(info.version, "legacy");
        assert_eq!(info.original_size, test_data.len() as u64);
    }

    #[tokio::test]
    async fn test_large_file_encryption() {
        let config = create_test_config("legacy", "bitwise_not");
        let encryptor = EncryptionFactory::create_encryptor(&config).unwrap();
        
        let temp_dir = tempdir().unwrap();
        let input_path = temp_dir.path().join("large_test.mp3");
        let encrypted_path = temp_dir.path().join("large_test.bin");
        let output_path = temp_dir.path().join("large_test_decrypted.mp3");
        
        // 创建较大的测试数据 (1MB)
        let large_data: Vec<u8> = (0..1024*1024).map(|i| (i % 256) as u8).collect();
        fs::write(&input_path, &large_data).unwrap();
        
        // 加密
        encryptor.encrypt_file(
            input_path.to_str().unwrap(),
            encrypted_path.to_str().unwrap(),
            "large_test_file"
        ).await.unwrap();
        
        // 解密
        encryptor.decrypt_file(
            encrypted_path.to_str().unwrap(),
            output_path.to_str().unwrap()
        ).await.unwrap();
        
        // 验证
        let decrypted_data = fs::read(&output_path).unwrap();
        assert_eq!(decrypted_data.len(), large_data.len());
        assert_eq!(decrypted_data, large_data);
    }

    #[tokio::test]
    async fn test_edge_cases() {
        let config = create_test_config("legacy", "bitwise_not");
        let encryptor = EncryptionFactory::create_encryptor(&config).unwrap();
        
        let temp_dir = tempdir().unwrap();
        
        // 测试空文件
        let empty_input = temp_dir.path().join("empty.mp3");
        let empty_encrypted = temp_dir.path().join("empty.bin");
        let empty_output = temp_dir.path().join("empty_decrypted.mp3");
        
        fs::write(&empty_input, b"").unwrap();
        
        encryptor.encrypt_file(
            empty_input.to_str().unwrap(),
            empty_encrypted.to_str().unwrap(),
            "empty_file"
        ).await.unwrap();
        
        encryptor.decrypt_file(
            empty_encrypted.to_str().unwrap(),
            empty_output.to_str().unwrap()
        ).await.unwrap();
        
        let empty_result = fs::read(&empty_output).unwrap();
        assert_eq!(empty_result.len(), 0);
        
        // 测试单字节文件
        let single_input = temp_dir.path().join("single.mp3");
        let single_encrypted = temp_dir.path().join("single.bin");
        let single_output = temp_dir.path().join("single_decrypted.mp3");
        
        fs::write(&single_input, b"A").unwrap();
        
        encryptor.encrypt_file(
            single_input.to_str().unwrap(),
            single_encrypted.to_str().unwrap(),
            "single_file"
        ).await.unwrap();
        
        encryptor.decrypt_file(
            single_encrypted.to_str().unwrap(),
            single_output.to_str().unwrap()
        ).await.unwrap();
        
        let single_result = fs::read(&single_output).unwrap();
        assert_eq!(single_result, b"A");
    }

    #[test]
    fn test_config_defaults() {
        // 测试默认配置创建  
        let config = EncryptionConfig {
            version: "v1".to_string(),
            method: "aes_gcm".to_string(),
        };

        assert_eq!(config.version, "v1");
        assert_eq!(config.method, "aes_gcm");
    }
}
