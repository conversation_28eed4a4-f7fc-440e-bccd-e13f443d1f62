//! 老版按位取反加密器
//! 
//! 兼容原始 .NET 项目的简单加密方式，使用按位取反操作

use anyhow::{Result, anyhow};
use async_trait::async_trait;
use log::{info, debug};
use std::fs::File;
use std::io::Read;

use super::{Encryptor, FileInfo};

/// 老版按位取反加密器
/// 
/// 实现与原始 .NET 项目兼容的简单加密方式：
/// - 对每个字节执行按位取反操作 (~byte)
/// - 加密和解密是同一操作
/// - 没有密钥，没有头部信息
/// - 文件大小不变
#[derive(Clone)]
pub struct LegacyEncryption;

impl LegacyEncryption {
    /// 创建新的老版加密器
    pub fn new() -> Self {
        Self
    }
    
    /// 执行按位取反操作
    /// 
    /// 这是老版加密的核心逻辑，对应原始 C# 代码：
    /// ```csharp
    /// for (int i=0; i< size; i++)
    /// {
    ///     encrypted[i] = (byte)~original[i];
    /// }
    /// ```
    fn bitwise_not_transform(data: &[u8]) -> Vec<u8> {
        data.iter().map(|&byte| !byte).collect()
    }
}

#[async_trait]
impl Encryptor for LegacyEncryption {
    /// 加密文件
    /// 
    /// 使用按位取反操作加密文件
    async fn encrypt_file(&self, input_path: &str, output_path: &str, file_id: &str) -> Result<()> {
        info!("🔐 开始老版加密: {} -> {} (文件ID: {})", input_path, output_path, file_id);
        
        // 读取原始文件
        let input_data = std::fs::read(input_path)
            .map_err(|e| anyhow!("读取输入文件失败 {}: {}", input_path, e))?;
        
        // 执行按位取反加密
        let encrypted_data = Self::bitwise_not_transform(&input_data);
        
        // 写入加密文件
        std::fs::write(output_path, encrypted_data)
            .map_err(|e| anyhow!("写入加密文件失败 {}: {}", output_path, e))?;
        
        info!("✅ 老版加密完成: {} ({} bytes)", output_path, input_data.len());
        Ok(())
    }
    
    /// 解密文件
    /// 
    /// 由于按位取反的特性，解密操作与加密操作完全相同
    async fn decrypt_file(&self, encrypted_path: &str, output_path: &str) -> Result<()> {
        info!("🔓 开始老版解密: {} -> {}", encrypted_path, output_path);
        
        // 读取加密文件
        let encrypted_data = std::fs::read(encrypted_path)
            .map_err(|e| anyhow!("读取加密文件失败 {}: {}", encrypted_path, e))?;
        
        // 执行按位取反解密（与加密操作相同）
        let decrypted_data = Self::bitwise_not_transform(&encrypted_data);
        
        // 写入解密文件
        std::fs::write(output_path, decrypted_data)
            .map_err(|e| anyhow!("写入解密文件失败 {}: {}", output_path, e))?;
        
        info!("✅ 老版解密完成: {} ({} bytes)", output_path, encrypted_data.len());
        Ok(())
    }
    
    /// 解密文件的指定范围
    /// 
    /// 老版加密支持随机访问，因为没有头部信息且是简单的字节变换
    async fn decrypt_range(&self, encrypted_path: &str, start: u64, length: u64) -> Result<Vec<u8>> {
        debug!("🔍 老版范围解密: {} bytes from offset {}", length, start);
        
        // 打开加密文件
        let mut file = File::open(encrypted_path)
            .map_err(|e| anyhow!("打开加密文件失败 {}: {}", encrypted_path, e))?;
        
        // 获取文件大小
        let file_size = file.metadata()
            .map_err(|e| anyhow!("获取文件元数据失败: {}", e))?
            .len();
        
        // 验证范围
        if start >= file_size {
            return Err(anyhow!("起始位置 {} 超出文件大小 {}", start, file_size));
        }
        
        // 计算实际读取长度
        let actual_length = std::cmp::min(length, file_size - start);
        
        // 定位到起始位置
        use std::io::Seek;
        file.seek(std::io::SeekFrom::Start(start))
            .map_err(|e| anyhow!("文件定位失败: {}", e))?;
        
        // 读取指定范围的数据
        let mut encrypted_range = vec![0u8; actual_length as usize];
        file.read_exact(&mut encrypted_range)
            .map_err(|e| anyhow!("读取文件范围失败: {}", e))?;
        
        // 解密数据
        let decrypted_range = Self::bitwise_not_transform(&encrypted_range);
        
        debug!("✅ 老版范围解密完成: {} bytes", decrypted_range.len());
        Ok(decrypted_range)
    }
    
    /// 获取文件信息
    /// 
    /// 老版加密没有头部信息，只能返回基本的文件信息
    async fn get_file_info(&self, encrypted_path: &str) -> Result<Option<FileInfo>> {
        let metadata = std::fs::metadata(encrypted_path)
            .map_err(|e| anyhow!("获取文件元数据失败 {}: {}", encrypted_path, e))?;
        
        // 老版加密文件大小等于原始文件大小
        let file_size = metadata.len();
        
        // 从文件路径提取文件ID（简化实现）
        let file_id = std::path::Path::new(encrypted_path)
            .file_stem()
            .and_then(|s| s.to_str())
            .unwrap_or("unknown")
            .to_string();
        
        Ok(Some(FileInfo {
            file_id,
            original_size: file_size,
            version: "legacy".to_string(),
        }))
    }
}

impl Default for LegacyEncryption {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;
    use tempfile::tempdir;
    
    #[tokio::test]
    async fn test_legacy_encryption_roundtrip() {
        let encryptor = LegacyEncryption::new();
        let temp_dir = tempdir().unwrap();
        
        // 创建测试文件
        let input_path = temp_dir.path().join("test.mp3");
        let encrypted_path = temp_dir.path().join("test.bin");
        let output_path = temp_dir.path().join("test_decrypted.mp3");
        
        let test_data = b"Hello, World! This is test audio data.";
        fs::write(&input_path, test_data).unwrap();
        
        // 加密
        encryptor.encrypt_file(
            input_path.to_str().unwrap(),
            encrypted_path.to_str().unwrap(),
            "test_file"
        ).await.unwrap();
        
        // 验证加密文件存在且大小相同
        assert!(encrypted_path.exists());
        assert_eq!(fs::metadata(&encrypted_path).unwrap().len(), test_data.len() as u64);
        
        // 解密
        encryptor.decrypt_file(
            encrypted_path.to_str().unwrap(),
            output_path.to_str().unwrap()
        ).await.unwrap();
        
        // 验证解密结果
        let decrypted_data = fs::read(&output_path).unwrap();
        assert_eq!(decrypted_data, test_data);
    }
    
    #[tokio::test]
    async fn test_legacy_encryption_range_decrypt() {
        let encryptor = LegacyEncryption::new();
        let temp_dir = tempdir().unwrap();
        
        // 创建测试文件
        let input_path = temp_dir.path().join("test.mp3");
        let encrypted_path = temp_dir.path().join("test.bin");
        
        let test_data = b"0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        fs::write(&input_path, test_data).unwrap();
        
        // 加密
        encryptor.encrypt_file(
            input_path.to_str().unwrap(),
            encrypted_path.to_str().unwrap(),
            "test_file"
        ).await.unwrap();
        
        // 范围解密
        let range_data = encryptor.decrypt_range(
            encrypted_path.to_str().unwrap(),
            10,
            10
        ).await.unwrap();
        
        // 验证范围解密结果
        assert_eq!(range_data, &test_data[10..20]);
    }
    
    #[test]
    fn test_bitwise_not_transform() {
        let input = vec![0x00, 0xFF, 0xAA, 0x55];
        let expected = vec![0xFF, 0x00, 0x55, 0xAA];
        
        let result = LegacyEncryption::bitwise_not_transform(&input);
        assert_eq!(result, expected);
        
        // 验证双重变换回到原值
        let double_transform = LegacyEncryption::bitwise_not_transform(&result);
        assert_eq!(double_transform, input);
    }
}
