use aes_gcm::{Aes256Gcm, Key, Nonce, KeyInit, aead::Aead};
use anyhow::{Result, anyhow};
use sha2::{Sha256, Digest};
use std::fs::File;
use std::io::{Read, Write};
use log::{info, debug};

/// 音频文件加密器
/// 使用 AES-256-GCM 模式，提供认证加密
#[derive(Debug, <PERSON>lone)]
pub struct AudioEncryption {
    master_key: [u8; 32],
}

/// 加密文件头部信息
#[derive(Debug, <PERSON>lone)]
pub struct EncryptionHeader {
    pub version: u8,
    pub file_id: String,
    pub nonce: [u8; 12],  // AES-GCM 使用 12 字节 nonce
    pub original_size: u64,
    pub checksum: [u8; 32],
}

impl AudioEncryption {
    /// 创建新的音频加密器
    pub fn new(master_key: [u8; 32]) -> Self {
        Self { master_key }
    }

    /// 从环境变量或配置文件加载主密钥
    pub fn from_config() -> Result<Self> {
        // 在生产环境中，应该从安全的密钥管理服务获取
        let key_hex = std::env::var("AUDIO_MASTER_KEY")
            .map_err(|_| anyhow!("AUDIO_MASTER_KEY environment variable not set"))?;
        
        let key_bytes = hex::decode(key_hex)
            .map_err(|_| anyhow!("Invalid master key format"))?;
        
        if key_bytes.len() != 32 {
            return Err(anyhow!("Master key must be 32 bytes"));
        }

        let mut key = [0u8; 32];
        key.copy_from_slice(&key_bytes);
        
        Ok(Self::new(key))
    }

    /// 派生文件特定的加密密钥
    fn derive_file_key(&self, file_id: &str) -> [u8; 32] {
        let mut hasher = Sha256::new();
        hasher.update(&self.master_key);
        hasher.update(file_id.as_bytes());
        hasher.update(b"audio_encryption_v1");
        
        let result = hasher.finalize();
        let mut key = [0u8; 32];
        key.copy_from_slice(&result);
        key
    }

    /// 加密音频文件
    pub fn encrypt_file(&self, input_path: &str, output_path: &str, file_id: &str) -> Result<EncryptionHeader> {
        info!("开始加密音频文件: {} -> {}", input_path, output_path);

        // 读取原始文件
        let mut input_file = File::open(input_path)?;
        let mut input_data = Vec::new();
        input_file.read_to_end(&mut input_data)?;

        // 生成随机 nonce
        let nonce = self.generate_nonce();

        // 派生文件密钥
        let file_key = self.derive_file_key(file_id);

        // 计算原始文件校验和
        let mut hasher = Sha256::new();
        hasher.update(&input_data);
        let checksum = hasher.finalize();
        let mut checksum_array = [0u8; 32];
        checksum_array.copy_from_slice(&checksum);

        // 创建 AES-GCM 加密器
        let key = Key::<Aes256Gcm>::from_slice(&file_key);
        let cipher = Aes256Gcm::new(key);
        let nonce_ga = Nonce::from_slice(&nonce);

        // 加密数据
        let encrypted_data = cipher.encrypt(nonce_ga, input_data.as_ref())
            .map_err(|e| anyhow!("Encryption failed: {}", e))?;

        // 创建加密头部
        let header = EncryptionHeader {
            version: 1,
            file_id: file_id.to_string(),
            nonce,
            original_size: input_data.len() as u64,
            checksum: checksum_array,
        };

        // 写入加密文件
        let mut output_file = File::create(output_path)?;
        self.write_header(&mut output_file, &header)?;
        output_file.write_all(&encrypted_data)?;

        info!("音频文件加密完成: {}", output_path);
        Ok(header)
    }

    /// 解密音频文件的指定范围（注意：AES-GCM 不支持随机访问，需要完整解密）
    pub fn decrypt_range(&self, encrypted_path: &str, start: u64, length: u64) -> Result<Vec<u8>> {
        debug!("解密音频范围: {} bytes from offset {}", length, start);

        // AES-GCM 不支持随机访问，需要先完整解密
        let decrypted_data = self.decrypt_file_to_memory(encrypted_path)?;

        // 验证范围
        if start >= decrypted_data.len() as u64 {
            return Err(anyhow!("Start offset beyond file size"));
        }

        let actual_length = std::cmp::min(length, decrypted_data.len() as u64 - start);
        let end = start + actual_length;

        Ok(decrypted_data[start as usize..end as usize].to_vec())
    }

    /// 将加密文件解密到内存中
    fn decrypt_file_to_memory(&self, encrypted_path: &str) -> Result<Vec<u8>> {
        let mut file = File::open(encrypted_path)?;
        let header = self.read_header(&mut file)?;

        // 读取所有加密数据
        let mut encrypted_data = Vec::new();
        file.read_to_end(&mut encrypted_data)?;

        // 派生文件密钥并解密
        let file_key = self.derive_file_key(&header.file_id);
        let key = Key::<Aes256Gcm>::from_slice(&file_key);
        let cipher = Aes256Gcm::new(key);
        let nonce = Nonce::from_slice(&header.nonce);

        let decrypted_data = cipher.decrypt(nonce, encrypted_data.as_ref())
            .map_err(|e| anyhow!("Decryption failed: {}", e))?;

        // 验证校验和
        let mut hasher = Sha256::new();
        hasher.update(&decrypted_data);
        let computed_checksum = hasher.finalize();

        if computed_checksum.as_slice() != &header.checksum {
            return Err(anyhow!("文件校验和验证失败"));
        }

        Ok(decrypted_data)
    }

    /// 完全解密音频文件
    pub fn decrypt_file(&self, encrypted_path: &str, output_path: &str) -> Result<()> {
        info!("开始解密音频文件: {} -> {}", encrypted_path, output_path);

        let decrypted_data = self.decrypt_file_to_memory(encrypted_path)?;

        // 写入解密文件
        let mut output_file = File::create(output_path)?;
        output_file.write_all(&decrypted_data)?;

        info!("音频文件解密完成: {}", output_path);
        Ok(())
    }

    /// 生成随机 nonce
    fn generate_nonce(&self) -> [u8; 12] {
        use rand::RngCore;
        let mut nonce = [0u8; 12];
        rand::thread_rng().fill_bytes(&mut nonce);
        nonce
    }

    /// 写入加密头部
    fn write_header(&self, file: &mut File, header: &EncryptionHeader) -> Result<()> {
        // 头部格式：版本(1) + 文件ID长度(1) + 文件ID + nonce(12) + 原始大小(8) + 校验和(32)
        file.write_all(&[header.version])?;
        file.write_all(&[header.file_id.len() as u8])?;
        file.write_all(header.file_id.as_bytes())?;
        file.write_all(&header.nonce)?;
        file.write_all(&header.original_size.to_be_bytes())?;
        file.write_all(&header.checksum)?;
        Ok(())
    }

    /// 读取加密头部
    fn read_header(&self, file: &mut File) -> Result<EncryptionHeader> {
        let mut version = [0u8; 1];
        file.read_exact(&mut version)?;

        let mut id_len = [0u8; 1];
        file.read_exact(&mut id_len)?;

        let mut file_id_bytes = vec![0u8; id_len[0] as usize];
        file.read_exact(&mut file_id_bytes)?;
        let file_id = String::from_utf8(file_id_bytes)?;

        let mut nonce = [0u8; 12];
        file.read_exact(&mut nonce)?;

        let mut size_bytes = [0u8; 8];
        file.read_exact(&mut size_bytes)?;
        let original_size = u64::from_be_bytes(size_bytes);

        let mut checksum = [0u8; 32];
        file.read_exact(&mut checksum)?;

        Ok(EncryptionHeader {
            version: version[0],
            file_id,
            nonce,
            original_size,
            checksum,
        })
    }

    /// 获取加密文件信息
    pub fn get_file_info(&self, encrypted_path: &str) -> Result<EncryptionHeader> {
        let mut file = std::fs::File::open(encrypted_path)?;
        self.read_header(&mut file)
    }

    /// 获取头部大小
    fn get_header_size(&self) -> u64 {
        // 版本(1) + ID长度(1) + ID + IV(16) + 大小(8) + 校验和(32)
        // 这里假设文件ID长度，实际应该动态计算
        64 // 简化计算，实际应该根据文件ID长度动态计算
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_audio_encryption_roundtrip() {
        let master_key = [0u8; 32]; // 测试密钥
        let encryption = AudioEncryption::new(master_key);

        // 使用临时文件
        let input_path = "/tmp/test.mp3";
        let encrypted_path = "/tmp/test.encrypted";
        let output_path = "/tmp/test_decrypted.mp3";

        // 创建测试文件
        std::fs::write(input_path, b"test audio data").unwrap();

        // 加密
        let _header = encryption.encrypt_file(
            input_path,
            encrypted_path,
            "test_file_id"
        ).unwrap();

        // 解密
        encryption.decrypt_file(
            encrypted_path,
            output_path
        ).unwrap();

        // 验证
        let original = std::fs::read(input_path).unwrap();
        let decrypted = std::fs::read(output_path).unwrap();
        assert_eq!(original, decrypted);

        // 清理
        let _ = std::fs::remove_file(input_path);
        let _ = std::fs::remove_file(encrypted_path);
        let _ = std::fs::remove_file(output_path);
    }
}
