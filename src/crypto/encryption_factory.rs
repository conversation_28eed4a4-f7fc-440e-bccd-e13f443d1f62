//! 加密工厂模块
//! 
//! 根据配置动态创建对应的加密器实例

use anyhow::{Result, anyhow};
use log::info;
use std::sync::Arc;

use crate::config::{EncryptionConfig, EncryptionVersion};
use super::{Encryptor, AudioEncryption, LegacyEncryption};

/// 加密工厂
/// 
/// 负责根据配置创建对应的加密器实例
pub struct EncryptionFactory;

impl EncryptionFactory {
    /// 根据配置创建加密器
    /// 
    /// # 参数
    /// * `config` - 加密配置
    /// 
    /// # 返回
    /// 返回对应的加密器实例
    pub fn create_encryptor(config: &EncryptionConfig) -> Result<Arc<dyn Encryptor>> {
        let version = EncryptionVersion::from(config.version.as_str());
        
        match version {
            EncryptionVersion::Legacy => {
                info!("🔧 创建老版按位取反加密器 (method: {})", config.method);
                
                // 验证方法配置
                if config.method != "bitwise_not" {
                    return Err(anyhow!(
                        "老版加密只支持 'bitwise_not' 方法，当前配置: '{}'", 
                        config.method
                    ));
                }
                
                Ok(Arc::new(LegacyEncryption::new()))
            }
            
            EncryptionVersion::V1 => {
                info!("🔧 创建新版 AES-GCM 加密器 (method: {})", config.method);
                
                // 验证方法配置
                if config.method != "aes_gcm" {
                    return Err(anyhow!(
                        "新版加密只支持 'aes_gcm' 方法，当前配置: '{}'", 
                        config.method
                    ));
                }
                
                // 创建 AES-GCM 加密器
                let audio_encryption = AudioEncryption::from_config()
                    .map_err(|e| anyhow!("创建 AES-GCM 加密器失败: {}", e))?;
                
                Ok(Arc::new(AudioEncryptionWrapper::new(audio_encryption)))
            }
        }
    }
    
    /// 根据版本字符串创建加密器（便捷方法）
    /// 
    /// # 参数
    /// * `version` - 版本字符串 ("legacy" 或 "v1")
    /// 
    /// # 返回
    /// 返回对应的加密器实例
    pub fn create_encryptor_by_version(version: &str) -> Result<Arc<dyn Encryptor>> {
        let config = match version.to_lowercase().as_str() {
            "legacy" => EncryptionConfig {
                version: "legacy".to_string(),
                method: "bitwise_not".to_string(),
            },
            "v1" => EncryptionConfig {
                version: "v1".to_string(),
                method: "aes_gcm".to_string(),
            },
            _ => return Err(anyhow!("不支持的加密版本: {}", version)),
        };
        
        Self::create_encryptor(&config)
    }
    
    /// 验证加密配置的有效性
    /// 
    /// # 参数
    /// * `config` - 加密配置
    /// 
    /// # 返回
    /// 配置有效返回 Ok(())，否则返回错误
    pub fn validate_config(config: &EncryptionConfig) -> Result<()> {
        let version = EncryptionVersion::from(config.version.as_str());
        
        match version {
            EncryptionVersion::Legacy => {
                if config.method != "bitwise_not" {
                    return Err(anyhow!(
                        "老版加密配置错误: method 应为 'bitwise_not'，当前为 '{}'",
                        config.method
                    ));
                }
            }

            EncryptionVersion::V1 => {
                if config.method != "aes_gcm" {
                    return Err(anyhow!(
                        "新版加密配置错误: method 应为 'aes_gcm'，当前为 '{}'",
                        config.method
                    ));
                }
            }
        }
        
        Ok(())
    }
}

/// AudioEncryption 的包装器，实现 Encryptor trait
/// 
/// 由于 AudioEncryption 使用同步接口，需要包装成异步接口
struct AudioEncryptionWrapper {
    inner: AudioEncryption,
}

impl AudioEncryptionWrapper {
    fn new(inner: AudioEncryption) -> Self {
        Self { inner }
    }
}

#[async_trait::async_trait]
impl Encryptor for AudioEncryptionWrapper {
    async fn encrypt_file(&self, input_path: &str, output_path: &str, file_id: &str) -> Result<()> {
        // 在异步上下文中运行同步代码
        let inner = self.inner.clone();
        let input_path = input_path.to_string();
        let output_path = output_path.to_string();
        let file_id = file_id.to_string();
        
        tokio::task::spawn_blocking(move || {
            inner.encrypt_file(&input_path, &output_path, &file_id)
                .map(|_| ()) // 忽略返回的 EncryptionHeader
        }).await
        .map_err(|e| anyhow!("异步任务执行失败: {}", e))?
    }
    
    async fn decrypt_file(&self, encrypted_path: &str, output_path: &str) -> Result<()> {
        let inner = self.inner.clone();
        let encrypted_path = encrypted_path.to_string();
        let output_path = output_path.to_string();
        
        tokio::task::spawn_blocking(move || {
            inner.decrypt_file(&encrypted_path, &output_path)
        }).await
        .map_err(|e| anyhow!("异步任务执行失败: {}", e))?
    }
    
    async fn decrypt_range(&self, encrypted_path: &str, start: u64, length: u64) -> Result<Vec<u8>> {
        let inner = self.inner.clone();
        let encrypted_path = encrypted_path.to_string();
        
        tokio::task::spawn_blocking(move || {
            inner.decrypt_range(&encrypted_path, start, length)
        }).await
        .map_err(|e| anyhow!("异步任务执行失败: {}", e))?
    }
    
    async fn get_file_info(&self, encrypted_path: &str) -> Result<Option<super::FileInfo>> {
        let inner = self.inner.clone();
        let encrypted_path = encrypted_path.to_string();
        
        let header = tokio::task::spawn_blocking(move || {
            inner.get_file_info(&encrypted_path)
        }).await
        .map_err(|e| anyhow!("异步任务执行失败: {}", e))??;
        
        Ok(Some(super::FileInfo {
            file_id: header.file_id,
            original_size: header.original_size,
            version: format!("v{}", header.version),
        }))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::EncryptionConfig;
    
    #[test]
    fn test_create_legacy_encryptor() {
        let config = EncryptionConfig {
            version: "legacy".to_string(),
            method: "bitwise_not".to_string(),
        };

        let encryptor = EncryptionFactory::create_encryptor(&config);
        assert!(encryptor.is_ok());
    }
    
    #[test]
    fn test_create_v1_encryptor_without_env() {
        let config = EncryptionConfig {
            version: "v1".to_string(),
            method: "aes_gcm".to_string(),
        };

        // 没有设置环境变量时应该失败
        let encryptor = EncryptionFactory::create_encryptor(&config);
        assert!(encryptor.is_err());
    }
    
    #[test]
    fn test_validate_config() {
        // 有效的老版配置
        let legacy_config = EncryptionConfig {
            version: "legacy".to_string(),
            method: "bitwise_not".to_string(),
        };
        assert!(EncryptionFactory::validate_config(&legacy_config).is_ok());

        // 无效的老版配置
        let invalid_legacy_config = EncryptionConfig {
            version: "legacy".to_string(),
            method: "aes_gcm".to_string(), // 错误的方法
        };
        assert!(EncryptionFactory::validate_config(&invalid_legacy_config).is_err());

        // 有效的新版配置
        let v1_config = EncryptionConfig {
            version: "v1".to_string(),
            method: "aes_gcm".to_string(),
        };
        assert!(EncryptionFactory::validate_config(&v1_config).is_ok());
    }
    
    #[test]
    fn test_create_encryptor_by_version() {
        // 创建老版加密器
        let legacy_encryptor = EncryptionFactory::create_encryptor_by_version("legacy");
        assert!(legacy_encryptor.is_ok());
        
        // 不支持的版本
        let invalid_encryptor = EncryptionFactory::create_encryptor_by_version("invalid");
        assert!(invalid_encryptor.is_err());
    }
}
