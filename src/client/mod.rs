use anyhow::{Result, Context};
use std::path::PathBuf;
use chrono::Utc;

use crate::config::Config;
use crate::importers::ImporterFactory;

pub async fn run_import_client(config_path: &str) -> Result<()> {
    let config_path = PathBuf::from(config_path);

    // 加载配置
    let config = Config::from_file(&config_path, "")
        .with_context(|| format!("Failed to load config from {:?}", config_path))?;

    let provider = config.provider.clone();
    println!("🎵 Music Import Tool for {}", provider.to_uppercase());
    println!("📁 Processing files in directory: {}", config.root_folder);

    // 生成日期标识
    let date = Utc::now().format("%Y%m%d").to_string();

    // 创建统一导入器
    let mut importer = ImporterFactory::create_importer(&provider, config, date)
        .with_context(|| format!("Failed to create importer for provider: {}", provider))?;

    println!("🔧 Initializing importer...");

    // 初始化导入器
    importer.initialize().await
        .with_context(|| "Failed to initialize importer")?;

    println!("🚀 Starting import process...");

    // 执行导入
    match importer.start_import_async().await {
        Ok(()) => {
            println!("✅ Import completed successfully");

            // 获取处理记录
            let records = importer.get_records();
            println!("📊 Processing summary:");
            for (status, files) in &records {
                if !files.is_empty() {
                    println!("  - {}: {} files", status, files.len());
                    for file in files {
                        println!("    📁 {}", file);
                    }
                }
            }

            // 显示总体统计
            let total_files: usize = records.values().map(|v| v.len()).sum();
            println!("📈 Total processed: {} files", total_files);

            // 发送邮件报告
            importer.send_email_report()
                .with_context(|| "Failed to send email report")?;

            // 清理资源
            importer.cleanup();
            println!("🎉 All done!");
            Ok(())
        }
        Err(error) => {
            println!("❌ Import failed: {}", error);

            // 即使失败也要清理资源
            importer.cleanup();
            anyhow::bail!("Import failed: {}", error)
        }
    }
}