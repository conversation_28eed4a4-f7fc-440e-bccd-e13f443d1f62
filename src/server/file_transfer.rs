use tonic::{Request, Response, Status, Streaming};
use tokio::fs::{File, OpenOptions};
use tokio::io::{AsyncSeekExt, AsyncWriteExt};
use tokio_stream::StreamExt;
use std::path::Path;
use log::{info, error};
use crate::utils::file_hash;

pub use crate::file_transfer::file_transfer_service_server::{FileTransferService, FileTransferServiceServer};
pub use crate::file_transfer::{FileChunk, UploadResponse, StatusRequest, StatusResponse};

pub struct FileTransferServer {
    upload_dir: String,
}

impl FileTransferServer {
    pub fn new(upload_dir: String) -> Self {
        // 确保上传目录存在
        if let Err(e) = std::fs::create_dir_all(&upload_dir) {
            error!("Failed to create upload directory {}: {}", upload_dir, e);
        }
        
        Self { upload_dir }
    }
}

#[tonic::async_trait]
impl FileTransferService for FileTransferServer {
    async fn upload_file(
        &self,
        request: Request<Streaming<FileChunk>>,
    ) -> Result<Response<UploadResponse>, Status> {
        let mut stream = request.into_inner();
        let mut total_bytes = 0u64;
        let mut current_file: Option<File> = None;
        let mut file_id = String::new();
        let mut file_name = String::new();

        info!("Starting file upload stream");

        while let Some(chunk) = stream.next().await {
            let chunk = chunk.map_err(|e| {
                error!("Stream error: {}", e);
                Status::internal(format!("Stream error: {}", e))
            })?;
            
            if file_id.is_empty() {
                file_id = chunk.file_id.clone();
                file_name = chunk.file_name.clone();
                
                let file_path = Path::new(&self.upload_dir).join(&file_name);
                
                // 创建父目录
                if let Some(parent) = file_path.parent() {
                    tokio::fs::create_dir_all(parent).await
                        .map_err(|e| Status::internal(format!("Failed to create directory: {}", e)))?;
                }
                
                current_file = Some(
                    OpenOptions::new()
                        .create(true)
                        .write(true)
                        .open(&file_path)
                        .await
                        .map_err(|e| Status::internal(format!("Failed to create file {}: {}", file_path.display(), e)))?
                );
                
                info!("Created file: {}", file_path.display());
            }

            if let Some(ref mut file) = current_file {
                file.seek(std::io::SeekFrom::Start(chunk.offset))
                    .await
                    .map_err(|e| Status::internal(format!("Seek failed: {}", e)))?;
                
                file.write_all(&chunk.data)
                    .await
                    .map_err(|e| Status::internal(format!("Write failed: {}", e)))?;
                
                total_bytes += chunk.data.len() as u64;
                
                if chunk.is_last_chunk {
                    file.flush().await
                        .map_err(|e| Status::internal(format!("Flush failed: {}", e)))?;
                    info!("Upload completed: {} ({} bytes)", file_name, total_bytes);
                    break;
                }
            }
        }

        Ok(Response::new(UploadResponse {
            success: true,
            message: format!("Upload completed: {}", file_name),
            bytes_received: total_bytes,
        }))
    }

    async fn check_upload_status(
        &self,
        request: Request<StatusRequest>,
    ) -> Result<Response<StatusResponse>, Status> {
        let req = request.into_inner();
        let file_path = Path::new(&self.upload_dir).join(&req.file_id);

        match tokio::fs::metadata(&file_path).await {
            Ok(metadata) => {
                let file_size = metadata.len();

                // 计算服务器端文件校验和
                let server_hash = match file_hash::calculate_file_hash_sync(&file_path) {
                    Ok(hash) => hash,
                    Err(e) => {
                        error!("Failed to calculate server file hash: {}", e);
                        return Ok(Response::new(StatusResponse {
                            bytes_uploaded: 0,
                            exists: false,
                            is_complete: false,
                            server_hash: String::new(),
                        }));
                    }
                };

                // 检查文件是否完整（大小匹配且校验和匹配）
                let is_complete = file_size == req.file_size &&
                                 !req.file_hash.is_empty() &&
                                 server_hash == req.file_hash;

                info!("File status check: {} - {} bytes, complete: {}, hash match: {}",
                      req.file_id, file_size, is_complete, server_hash == req.file_hash);

                Ok(Response::new(StatusResponse {
                    bytes_uploaded: file_size,
                    exists: true,
                    is_complete,
                    server_hash,
                }))
            }
            Err(_) => {
                info!("File status check: {} - not found", req.file_id);
                Ok(Response::new(StatusResponse {
                    bytes_uploaded: 0,
                    exists: false,
                    is_complete: false,
                    server_hash: String::new(),
                }))
            }
        }
    }
}