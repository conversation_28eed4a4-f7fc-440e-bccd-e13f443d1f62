pub mod db;
pub mod process;
pub mod grpc_client;
pub mod file_hash;
pub mod path_generator;

pub use path_generator::PathGenerator;

use std::process::{Command, Stdio};

// 执行外部程序
#[allow(dead_code)]
pub fn execute_command(program: &str, args: &[&str]) -> Result<String, String> {
    let output = Command::new(program)
        .args(args)
        .stderr(Stdio::piped())
        .stdout(Stdio::piped())
        .output();
        
    match output {
        Ok(output) => {
            if output.status.success() {
                Ok(String::from_utf8_lossy(&output.stdout).to_string())
            } else {
                Err(String::from_utf8_lossy(&output.stderr).to_string())
            }
        }
        Err(e) => Err(format!("Failed to execute {}: {}", program, e)),
    }
}

// 计算文件MD5
#[allow(dead_code)]
pub fn calculate_md5(path: &str) -> Result<String, String> {
    use std::fs::File;
    use std::io::Read;
    use md5;
    
    let mut file = match File::open(path) {
        Ok(file) => file,
        Err(e) => return Err(format!("Failed to open file {}: {}", path, e)),
    };
    
    let mut buffer = Vec::new();
    if let Err(e) = file.read_to_end(&mut buffer) {
        return Err(format!("Failed to read file {}: {}", path, e));
    }
    
    let digest = md5::compute(&buffer);
    Ok(format!("{:x}", digest))
}
