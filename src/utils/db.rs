use anyhow::{Result, Context};
use mysql::{<PERSON>, PooledConn, Opts, OptsBuilder};
use mysql::prelude::Queryable;
use crate::config::DbConfig;

#[allow(dead_code)]
pub struct Database {
    pool: Pool,
}

#[allow(dead_code)]
impl Database {
    pub fn new(config: &DbConfig) -> Result<Self> {
        let opts = OptsBuilder::new()
            .ip_or_hostname(Some(&config.host))
            .user(Some(&config.user))
            .pass(Some(&config.password))
            .db_name(Some(&config.database))
            .tcp_port(config.port);

        let pool = Pool::new(Opts::from(opts))
            .context(format!(
                "Failed to create database connection pool to {}@{}:{}/{}",
                config.user, config.host, config.port, config.database
            ))?;

        // 测试连接
        match pool.get_conn() {
            Ok(mut conn) => {
                match conn.query_drop("SELECT 1") {
                    Ok(_) => {},
                    Err(e) => {
                        return Err(anyhow::anyhow!("Database connection test failed: {}", e));
                    }
                }
            }
            Err(e) => {
                return Err(anyhow::anyhow!("Failed to get test connection: {}", e));
            }
        }

        Ok(Database { pool })
    }
    
    pub fn get_conn(&self) -> Result<PooledConn> {
        self.pool.get_conn().context("Failed to get database connection from pool")
    }
    
    pub async fn execute<P>(&self, query: &str, params: P) -> Result<u64>
    where
        P: Into<mysql::Params>,
    {
        let mut conn = self.get_conn()?;

        let result = conn.exec_iter(query, params)
            .context("Failed to execute query")?;

        Ok(result.affected_rows())
    }

    pub async fn execute_insert<P>(&self, query: &str, params: P) -> Result<i64>
    where
        P: Into<mysql::Params>,
    {
        let mut conn = self.get_conn()?;

        match conn.exec_drop(query, params) {
            Ok(_) => {},
            Err(e) => {
                return Err(anyhow::anyhow!(
                    "Failed to execute insert query: {}\nMySQL Error: {}",
                    query, e
                ));
            }
        }

        Ok(conn.last_insert_id() as i64)
    }

    pub async fn query<P>(&self, query: &str, params: P) -> Result<Vec<mysql::Row>>
    where
        P: Into<mysql::Params>,
    {
        let mut conn = self.get_conn()?;

        let result: Vec<mysql::Row> = conn.exec(query, params)
            .context(format!("Failed to execute query: {}", query))?;

        Ok(result)
    }
}