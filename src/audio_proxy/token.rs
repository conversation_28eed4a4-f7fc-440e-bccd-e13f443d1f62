use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};

/// JWT 风格的令牌实现
#[derive(Debug, Serialize, Deserialize)]
pub struct JwtClaims {
    pub sub: String,    // 用户ID
    pub aud: String,    // 文件ID
    pub exp: u64,       // 过期时间
    pub iat: u64,       // 签发时间
    pub permissions: Vec<String>,
}

impl JwtClaims {
    pub fn new(user_id: String, file_id: String, expires_in: u64) -> Self {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        Self {
            sub: user_id,
            aud: file_id,
            exp: now + expires_in,
            iat: now,
            permissions: vec!["read".to_string()],
        }
    }

    pub fn is_expired(&self) -> bool {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
        
        now > self.exp
    }
}
