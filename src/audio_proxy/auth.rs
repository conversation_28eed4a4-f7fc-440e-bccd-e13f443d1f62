use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use std::time::{SystemTime, UNIX_EPOCH};
use log::{debug, warn};

/// 认证服务
pub struct AuthService {
    secret_key: String,
}

/// 访问令牌数据
#[derive(Debug, Serialize, Deserialize)]
pub struct TokenData {
    pub file_id: String,
    pub user_id: String,
    pub expires_at: u64,
    pub issued_at: u64,
    pub permissions: Vec<String>,
}

/// 令牌生成请求
#[derive(Debug, Deserialize)]
pub struct TokenRequest {
    pub file_id: String,
    pub user_id: String,
    pub expires_in: Option<u64>, // 秒
    pub permissions: Option<Vec<String>>,
}

impl AuthService {
    pub fn new() -> Self {
        let secret_key = std::env::var("AUTH_SECRET_KEY")
            .unwrap_or_else(|_| "default_secret_key_change_in_production".to_string());
        
        Self { secret_key }
    }

    /// 生成访问令牌
    pub fn generate_token(&self, request: TokenRequest) -> Result<String> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)?
            .as_secs();

        let expires_in = request.expires_in.unwrap_or(3600); // 默认1小时
        let token_data = TokenData {
            file_id: request.file_id,
            user_id: request.user_id,
            expires_at: now + expires_in,
            issued_at: now,
            permissions: request.permissions.unwrap_or_else(|| vec!["read".to_string()]),
        };

        // 简化的令牌生成（生产环境应使用 JWT）
        let payload = serde_json::to_string(&token_data)?;
        let signature = self.sign_payload(&payload)?;

        use base64::{Engine as _, engine::general_purpose};
        let token = format!("{}.{}",
                           general_purpose::STANDARD.encode(&payload),
                           general_purpose::STANDARD.encode(&signature));

        debug!("生成访问令牌: 用户={}, 文件={}, 过期时间={}", 
               token_data.user_id, token_data.file_id, token_data.expires_at);

        Ok(token)
    }

    /// 验证访问令牌
    pub fn verify_token(&self, token: &str) -> Result<TokenData> {
        let parts: Vec<&str> = token.split('.').collect();
        if parts.len() != 2 {
            return Err(anyhow!("Invalid token format"));
        }

        use base64::{Engine as _, engine::general_purpose};
        let payload = general_purpose::STANDARD.decode(parts[0])?;
        let signature = general_purpose::STANDARD.decode(parts[1])?;

        // 验证签名
        let expected_signature = self.sign_payload(&String::from_utf8(payload.clone())?)?;
        if signature != expected_signature {
            warn!("令牌签名验证失败");
            return Err(anyhow!("Invalid token signature"));
        }

        // 解析令牌数据
        let token_data: TokenData = serde_json::from_slice(&payload)?;

        debug!("验证访问令牌: 用户={}, 文件={}", 
               token_data.user_id, token_data.file_id);

        Ok(token_data)
    }

    /// 检查用户权限
    pub fn check_permission(&self, token_data: &TokenData, required_permission: &str) -> bool {
        token_data.permissions.contains(&required_permission.to_string()) ||
        token_data.permissions.contains(&"admin".to_string())
    }

    /// 签名载荷
    fn sign_payload(&self, payload: &str) -> Result<Vec<u8>> {
        use hmac::{Hmac, Mac};
        use sha2::Sha256;

        type HmacSha256 = Hmac<Sha256>;
        
        let mut mac = HmacSha256::new_from_slice(self.secret_key.as_bytes())
            .map_err(|_| anyhow!("Invalid secret key"))?;
        
        mac.update(payload.as_bytes());
        Ok(mac.finalize().into_bytes().to_vec())
    }
}

impl TokenData {
    /// 检查令牌是否过期
    pub fn is_expired(&self) -> bool {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        now > self.expires_at
    }

    /// 获取剩余有效时间（秒）
    pub fn remaining_time(&self) -> u64 {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        
        if now >= self.expires_at {
            0
        } else {
            self.expires_at - now
        }
    }
}

/// PHP 集成助手
pub struct PhpIntegration;

impl PhpIntegration {
    /// 生成 PHP 认证代码
    pub fn generate_auth_code() -> String {
        r#"
<?php

class AudioAuthClient {
    private $authUrl;
    private $apiKey;
    
    public function __construct($authUrl, $apiKey) {
        $this->authUrl = rtrim($authUrl, '/');
        $this->apiKey = $apiKey;
    }
    
    /**
     * 生成音频访问令牌
     */
    public function generateAudioToken($fileId, $userId, $expiresIn = 3600, $permissions = ['read']) {
        $data = [
            'file_id' => $fileId,
            'user_id' => $userId,
            'expires_in' => $expiresIn,
            'permissions' => $permissions
        ];
        
        $response = $this->makeRequest('/auth/token', $data);
        return $response['token'] ?? null;
    }
    
    /**
     * 验证令牌
     */
    public function verifyToken($token) {
        $data = ['token' => $token];
        return $this->makeRequest('/auth/verify', $data);
    }
    
    /**
     * 生成完整的音频URL
     */
    public function generateSecureAudioUrl($fileId, $userId, $expiresIn = 3600) {
        $token = $this->generateAudioToken($fileId, $userId, $expiresIn);
        if (!$token) {
            throw new Exception('Failed to generate audio token');
        }
        
        return $this->authUrl . '/audio/' . $fileId . '?token=' . urlencode($token);
    }
    
    /**
     * 发送HTTP请求
     */
    private function makeRequest($endpoint, $data) {
        $url = $this->authUrl . $endpoint;
        
        $options = [
            'http' => [
                'method' => 'POST',
                'header' => [
                    'Content-Type: application/json',
                    'Authorization: Bearer ' . $this->apiKey
                ],
                'content' => json_encode($data)
            ]
        ];
        
        $context = stream_context_create($options);
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new Exception('Request failed');
        }
        
        return json_decode($response, true);
    }
}

// 使用示例
/*
$auth = new AudioAuthClient('http://localhost:8080', 'your-api-key');

// 为用户生成音频访问URL
$audioUrl = $auth->generateSecureAudioUrl('song123', 'user456', 3600);

// 在HTML中使用
echo '<audio controls><source src="' . htmlspecialchars($audioUrl) . '" type="audio/mpeg"></audio>';
*/
"#.to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_token_generation_and_verification() {
        let auth = AuthService::new();
        
        let request = TokenRequest {
            file_id: "test_file".to_string(),
            user_id: "test_user".to_string(),
            expires_in: Some(3600),
            permissions: Some(vec!["read".to_string()]),
        };

        let token = auth.generate_token(request).unwrap();
        let token_data = auth.verify_token(&token).unwrap();

        assert_eq!(token_data.file_id, "test_file");
        assert_eq!(token_data.user_id, "test_user");
        assert!(!token_data.is_expired());
    }

    #[test]
    fn test_permission_check() {
        let auth = AuthService::new();
        
        let token_data = TokenData {
            file_id: "test".to_string(),
            user_id: "test".to_string(),
            expires_at: u64::MAX,
            issued_at: 0,
            permissions: vec!["read".to_string()],
        };

        assert!(auth.check_permission(&token_data, "read"));
        assert!(!auth.check_permission(&token_data, "write"));
    }
}
