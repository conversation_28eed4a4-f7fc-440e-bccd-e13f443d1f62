//! 配置管理模块
//! 
//! 负责加载和管理应用程序配置，统一使用 JSON 格式

use anyhow::{Result, Context};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

/// 默认禁用上传前检查
fn default_check_before_upload() -> bool {
    false
}

/// 默认加密版本
fn default_encryption_version() -> String {
    "v1".to_string()
}

/// 默认加密方法
fn default_encryption_method() -> String {
    "aes_gcm".to_string()
}



/// 应用程序主配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// 提供商名称
    pub provider: String,
    /// 线程数量
    pub thread_count: usize,
    /// 是否需要发送邮件
    pub need_email: bool,
    /// 是否强制更新
    pub force_update: bool,
    /// 是否在上传前先检查数据存在性（默认false）
    pub check_before_upload: bool,
    /// 根目录路径
    pub root_folder: String,
    /// 日志目录路径
    pub log_folder: String,
    /// 数据库配置
    pub database: DbConfig,
    /// gRPC 配置（可选）
    pub grpc: Option<GrpcConfig>,
    /// 服务器配置（可选）
    pub server: Option<ServerConfig>,
    /// 邮件配置
    pub mail: MailConfig,
    /// 加密配置
    pub encryption: EncryptionConfig,
}

/// 数据库配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DbConfig {
    /// 数据库主机
    pub host: String,
    /// 用户名
    pub user: String,
    /// 密码
    pub password: String,
    /// 数据库名
    pub database: String,
    /// 端口号
    pub port: u16,
}

/// gRPC 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GrpcConfig {
    /// 服务器地址
    pub server_addr: String,
    /// 超时时间（秒）
    pub timeout_seconds: u64,
}

/// 服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    /// 监听主机
    pub host: String,
    /// 监听端口
    pub port: u16,
    /// 上传目录
    pub upload_dir: String,
}

/// 邮件配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MailConfig {
    /// SMTP 服务器
    pub smtp_server: String,
    /// SMTP 端口
    pub smtp_port: u16,
    /// 用户名
    pub username: String,
    /// 密码
    pub password: String,
    /// 发件人邮箱
    pub from_email: String,
    /// 收件人邮箱列表
    pub to_emails: Vec<String>,
}

/// 加密配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptionConfig {
    /// 加密版本 ("legacy" 或 "v1")
    #[serde(default = "default_encryption_version")]
    pub version: String,
    /// 加密方法 ("bitwise_not" 或 "aes_gcm")
    #[serde(default = "default_encryption_method")]
    pub method: String,
}

/// 加密版本枚举
#[derive(Debug, Clone, PartialEq)]
pub enum EncryptionVersion {
    Legacy,  // 老版按位取反
    V1,      // 新版 AES-GCM
}

impl From<&str> for EncryptionVersion {
    fn from(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "legacy" => EncryptionVersion::Legacy,
            "v1" => EncryptionVersion::V1,
            _ => EncryptionVersion::V1, // 默认使用新版
        }
    }
}



/// JSON 配置文件结构（用于反序列化）
#[derive(Debug, Deserialize)]
struct JsonConfig {
    pub provider: Option<String>,
    pub app_setting: AppSetting,
    pub source: Source,
    pub database: DbConfig,
    pub grpc: Option<GrpcConfig>,
    pub server: Option<ServerConfig>,
    pub mail: MailConfig,
    pub encryption: Option<EncryptionConfig>,
}

#[derive(Debug, Deserialize)]
struct AppSetting {
    pub thread_count: usize,
    pub need_email: bool,
    pub force_update: bool,
    #[serde(default = "default_check_before_upload")]
    pub check_before_upload: bool,
}

#[derive(Debug, Deserialize)]
struct Source {
    pub root_folder: String,
    pub log_folder: String,
}

impl Config {
    /// 从 JSON 文件加载配置
    pub fn from_file(path: &Path, provider: &str) -> Result<Self> {
        let content = fs::read_to_string(path)
            .with_context(|| format!("Failed to read config file: {:?}", path))?;
        
        Self::from_json_str(&content, provider)
    }
    
    /// 从 JSON 字符串加载配置
    pub fn from_json_str(json_str: &str, provider: &str) -> Result<Self> {
        let json_config: JsonConfig = serde_json::from_str(json_str)
            .with_context(|| "Failed to parse JSON config")?;

        // 使用配置文件中的 provider，如果没有则使用参数
        let provider = json_config.provider
            .as_deref()
            .unwrap_or(provider);

        // 如果没有提供加密配置，使用默认值
        let encryption = json_config.encryption.unwrap_or(EncryptionConfig {
            version: default_encryption_version(),
            method: default_encryption_method(),
        });

        Ok(Config {
            provider: provider.to_string(),
            thread_count: json_config.app_setting.thread_count,
            need_email: json_config.app_setting.need_email,
            force_update: json_config.app_setting.force_update,
            check_before_upload: json_config.app_setting.check_before_upload, // 从配置文件读取
            root_folder: json_config.source.root_folder,
            log_folder: json_config.source.log_folder,
            database: json_config.database,
            grpc: json_config.grpc,
            server: json_config.server,
            mail: json_config.mail,
            encryption,
        })
    }
    
    /// 创建默认配置（用于测试）
    pub fn default_for_testing(provider: &str) -> Self {
        Config {
            provider: provider.to_string(),
            thread_count: 4,
            need_email: false,
            force_update: false,
            check_before_upload: false, // 默认禁用上传前检查
            root_folder: "./test_data".to_string(),
            log_folder: "./logs".to_string(),
            database: DbConfig {
                host: "localhost".to_string(),
                user: "test_user".to_string(),
                password: "test_password".to_string(),
                database: "test_db".to_string(),
                port: 3306,
            },
            grpc: None,
            server: None,
            mail: MailConfig {
                smtp_server: "smtp.example.com".to_string(),
                smtp_port: 587,
                username: "<EMAIL>".to_string(),
                password: "test_password".to_string(),
                from_email: "<EMAIL>".to_string(),
                to_emails: vec!["<EMAIL>".to_string()],
            },
            encryption: EncryptionConfig {
                version: default_encryption_version(),
                method: default_encryption_method(),
            },
        }
    }
    
    /// 验证配置的有效性
    pub fn validate(&self) -> Result<()> {
        if self.provider.is_empty() {
            anyhow::bail!("Provider cannot be empty");
        }
        
        if self.thread_count == 0 {
            anyhow::bail!("Thread count must be greater than 0");
        }
        
        if self.root_folder.is_empty() {
            anyhow::bail!("Root folder cannot be empty");
        }
        
        if self.log_folder.is_empty() {
            anyhow::bail!("Log folder cannot be empty");
        }
        
        // 验证数据库配置
        if self.database.host.is_empty() {
            anyhow::bail!("Database host cannot be empty");
        }
        
        if self.database.user.is_empty() {
            anyhow::bail!("Database user cannot be empty");
        }
        
        if self.database.database.is_empty() {
            anyhow::bail!("Database name cannot be empty");
        }
        
        // 验证邮件配置
        if self.need_email {
            if self.mail.smtp_server.is_empty() {
                anyhow::bail!("SMTP server cannot be empty when email is enabled");
            }
            
            if self.mail.from_email.is_empty() {
                anyhow::bail!("From email cannot be empty when email is enabled");
            }
            
            if self.mail.to_emails.is_empty() {
                anyhow::bail!("To emails cannot be empty when email is enabled");
            }
        }
        
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_config_from_json() {
        let json_str = r#"
        {
          "provider": "sony",
          "app_setting": {
            "thread_count": 4,
            "need_email": false,
            "force_update": false
          },
          "source": {
            "root_folder": "./data/sony",
            "log_folder": "./logs"
          },
          "database": {
            "host": "localhost",
            "user": "music_user",
            "password": "password",
            "database": "music_import",
            "port": 3306
          },
          "mail": {
            "smtp_server": "smtp.example.com",
            "smtp_port": 587,
            "username": "<EMAIL>",
            "password": "password",
            "from_email": "<EMAIL>",
            "to_emails": ["<EMAIL>"]
          }
        }
        "#;
        
        let config = Config::from_json_str(json_str, "sony").unwrap();
        assert_eq!(config.provider, "sony");
        assert_eq!(config.thread_count, 4);
        assert_eq!(config.root_folder, "./data/sony");
        assert!(config.validate().is_ok());
    }
    
    #[test]
    fn test_default_config() {
        let config = Config::default_for_testing("test");
        assert_eq!(config.provider, "test");
        assert!(config.validate().is_ok());
    }
}
