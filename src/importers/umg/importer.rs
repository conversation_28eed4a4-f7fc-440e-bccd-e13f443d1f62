//! UMG 导入器实现

use anyhow::Result;
use log::{info, debug, error};
use std::path::Path;

use crate::config::Config;
use crate::importers::engine::{ImportEngine, DetailedImportResult};

use super::{UmgError, UmgDdexParser};
use super::utils::{convert_umg_to_standard, validate_umg_parsed_release};

/// UMG 导入器
pub struct UmgImporter {
    /// 配置信息
    config: Config,
    /// DDEX 解析器
    ddex_parser: UmgDdexParser,
    /// 导入引擎
    import_engine: Option<ImportEngine>,
}

impl UmgImporter {
    /// 创建新的 UMG 导入器
    pub fn new(config: Config) -> Self {
        Self {
            config,
            ddex_parser: UmgDdexParser::new(),
            import_engine: None,
        }
    }

    /// 初始化导入引擎
    pub fn with_import_engine(mut self, engine: ImportEngine) -> Self {
        self.import_engine = Some(engine);
        self
    }

    /// 导入单个目录
    pub async fn import_directory(&self, directory_path: &str) -> Result<DetailedImportResult> {
        info!("🎵 开始导入 UMG 目录: {}", directory_path);

        // 验证目录存在
        let dir_path = Path::new(directory_path);
        if !dir_path.exists() || !dir_path.is_dir() {
            return Err(anyhow::anyhow!("目录不存在或不是有效目录: {}", directory_path));
        }

        // 查找 XML 文件
        let xml_files = self.find_xml_files(directory_path)?;
        if xml_files.is_empty() {
            return Err(anyhow::anyhow!("目录中未找到 XML 文件: {}", directory_path));
        }

        if xml_files.len() > 1 {
            return Err(anyhow::anyhow!("目录中找到多个 XML 文件: {}", directory_path));
        }

        let xml_file = &xml_files[0];
        info!("📄 找到 XML 文件: {}", xml_file);

        // 解析 XML
        let parsed_release = self.parse_ddex_xml(xml_file)?;

        // 验证解析结果
        validate_umg_parsed_release(&parsed_release)?;

        // 转换为标准格式
        let standard_release = convert_umg_to_standard(parsed_release, xml_file)?;

        // 简化处理，直接返回成功
        Ok(DetailedImportResult {
            success: true,
            album_title: standard_release.album_info.title,
            grid: standard_release.album_info.grid.unwrap_or_else(|| "Unknown".to_string()),
            track_count: standard_release.tracks.len(),
            audio_resource_count: standard_release.audio_resources.len(),
            image_resource_count: standard_release.image_resources.len(),
            message: "UMG 导入完成".to_string(),
            errors: vec![],
        })
    }

    /// 批量导入多个目录
    pub async fn import_directories(&self, directories: &[String]) -> Result<Vec<DetailedImportResult>> {
        info!("🎵 开始批量导入 {} 个 UMG 目录", directories.len());

        let mut results = Vec::new();

        for directory in directories {
            match self.import_directory(directory).await {
                Ok(result) => {
                    info!("✅ 目录导入成功: {}", directory);
                    results.push(result);
                }
                Err(e) => {
                    error!("❌ 目录导入失败: {} - {}", directory, e);
                    results.push(DetailedImportResult {
                        success: false,
                        album_title: "Unknown".to_string(),
                        grid: "Unknown".to_string(),
                        track_count: 0,
                        audio_resource_count: 0,
                        image_resource_count: 0,
                        message: format!("导入失败: {}", e),
                        errors: vec![e.to_string()],
                    });
                }
            }
        }

        info!("🎉 批量导入完成，成功: {}, 失败: {}", 
              results.iter().filter(|r| r.success).count(),
              results.iter().filter(|r| !r.success).count());

        Ok(results)
    }

    /// 解析 DDEX XML 文件
    pub fn parse_ddex_xml(&self, xml_path: &str) -> Result<super::ParsedRelease, UmgError> {
        debug!("读取 XML 文件: {}", xml_path);
        let xml_content = std::fs::read_to_string(xml_path)
            .map_err(|e| UmgError::IoError(e))?;

        debug!("解析 UMG DDEX XML 内容");
        UmgDdexParser::parse_xml_with_context(&xml_content, xml_path)
    }

    /// 查找目录中的 XML 文件
    fn find_xml_files(&self, directory_path: &str) -> Result<Vec<String>> {
        let mut xml_files = Vec::new();

        for entry in std::fs::read_dir(directory_path)? {
            let entry = entry?;
            let path = entry.path();

            if path.is_file() {
                if let Some(extension) = path.extension() {
                    if extension.to_string_lossy().to_lowercase() == "xml" {
                        if let Some(path_str) = path.to_str() {
                            xml_files.push(path_str.to_string());
                        }
                    }
                }
            }
        }

        Ok(xml_files)
    }

    /// 获取配置信息
    pub fn get_config(&self) -> &Config {
        &self.config
    }

    /// 验证目录结构
    pub fn validate_directory_structure(&self, directory_path: &str) -> Result<()> {
        let dir_path = Path::new(directory_path);
        
        if !dir_path.exists() {
            return Err(anyhow::anyhow!("目录不存在: {}", directory_path));
        }

        if !dir_path.is_dir() {
            return Err(anyhow::anyhow!("路径不是目录: {}", directory_path));
        }

        // 检查是否有 XML 文件
        let xml_files = self.find_xml_files(directory_path)?;
        if xml_files.is_empty() {
            return Err(anyhow::anyhow!("目录中未找到 XML 文件: {}", directory_path));
        }

        if xml_files.len() > 1 {
            return Err(anyhow::anyhow!("目录中有多个 XML 文件: {}", directory_path));
        }

        debug!("目录结构验证通过: {}", directory_path);
        Ok(())
    }

    /// 获取目录摘要信息
    pub fn get_directory_summary(&self, directory_path: &str) -> Result<DirectorySummary> {
        self.validate_directory_structure(directory_path)?;

        let xml_files = self.find_xml_files(directory_path)?;
        let xml_file = &xml_files[0];

        // 解析 XML 获取基本信息
        let parsed_release = self.parse_ddex_xml(xml_file)?;

        // 统计文件信息
        let mut audio_file_count = 0;
        let mut image_file_count = 0;
        let mut total_size = 0u64;

        for entry in std::fs::read_dir(directory_path)? {
            let entry = entry?;
            let path = entry.path();

            if path.is_file() {
                let metadata = entry.metadata()?;
                total_size += metadata.len();

                if let Some(extension) = path.extension() {
                    let ext = extension.to_string_lossy().to_lowercase();
                    match ext.as_str() {
                        "mp3" | "wav" | "flac" | "m4a" | "aac" => audio_file_count += 1,
                        "jpg" | "jpeg" | "png" | "gif" | "bmp" => image_file_count += 1,
                        _ => {}
                    }
                }
            }
        }

        Ok(DirectorySummary {
            directory_path: directory_path.to_string(),
            xml_file: xml_file.clone(),
            album_title: parsed_release.album_info.title,
            grid: parsed_release.album_info.grid,
            track_count: parsed_release.tracks.len(),
            audio_file_count,
            image_file_count,
            total_size,
        })
    }
}

/// 目录摘要信息
#[derive(Debug, Clone)]
pub struct DirectorySummary {
    /// 目录路径
    pub directory_path: String,
    /// XML 文件路径
    pub xml_file: String,
    /// 专辑标题
    pub album_title: String,
    /// GRid
    pub grid: String,
    /// 曲目数量
    pub track_count: usize,
    /// 音频文件数量
    pub audio_file_count: usize,
    /// 图片文件数量
    pub image_file_count: usize,
    /// 总文件大小
    pub total_size: u64,
}

impl Default for UmgImporter {
    fn default() -> Self {
        Self::new(Config::default_for_testing("umg"))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;
    use std::fs;

    #[test]
    fn test_find_xml_files() {
        let temp_dir = tempdir().unwrap();
        let dir_path = temp_dir.path().to_str().unwrap();

        // 创建测试文件
        fs::write(temp_dir.path().join("test.xml"), "<?xml version=\"1.0\"?>").unwrap();
        fs::write(temp_dir.path().join("test.txt"), "text file").unwrap();
        fs::write(temp_dir.path().join("another.XML"), "<?xml version=\"1.0\"?>").unwrap();

        let importer = UmgImporter::default();
        let xml_files = importer.find_xml_files(dir_path).unwrap();

        assert_eq!(xml_files.len(), 2);
        assert!(xml_files.iter().any(|f| f.contains("test.xml")));
        assert!(xml_files.iter().any(|f| f.contains("another.XML")));
    }

    #[test]
    fn test_validate_directory_structure() {
        let temp_dir = tempdir().unwrap();
        let dir_path = temp_dir.path().to_str().unwrap();

        let importer = UmgImporter::default();

        // 没有 XML 文件时应该失败
        assert!(importer.validate_directory_structure(dir_path).is_err());

        // 创建 XML 文件后应该成功
        fs::write(temp_dir.path().join("test.xml"), "<?xml version=\"1.0\"?>").unwrap();
        assert!(importer.validate_directory_structure(dir_path).is_ok());

        // 多个 XML 文件时应该失败
        fs::write(temp_dir.path().join("test2.xml"), "<?xml version=\"1.0\"?>").unwrap();
        assert!(importer.validate_directory_structure(dir_path).is_err());
    }
}
