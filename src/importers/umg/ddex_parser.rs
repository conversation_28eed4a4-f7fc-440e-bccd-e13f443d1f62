//! UMG DDEX ERN XML 解析器
//!
//! 基于原始 UMG .NET 项目的解析逻辑，处理 UMG 特有的 XML 结构

use anyhow::Result;
use log::{info, debug, warn};
use roxmltree::{Document, Node};
use std::collections::HashMap;

use super::{UmgError, ParsedRelease, MessageHeader, AlbumInfo, TrackInfo, AudioResource, ImageResource, PartyInfo, ArtistInfo, ContributorInfo};

/// UMG DDEX ERN XML 解析器
#[derive(Debug, Clone)]
pub struct UmgDdexParser;

impl UmgDdexParser {
    /// 创建新的解析器实例
    pub fn new() -> Self {
        Self
    }

    /// 解析 XML 文件（实例方法）
    pub fn parse_xml(&self, xml_path: &str, _base_dir: &str) -> Result<super::ParsedRelease, UmgError> {
        let xml_content = std::fs::read_to_string(xml_path)
            .map_err(|e| UmgError::XmlParseError(format!("读取 XML 文件失败: {}", e)))?;

        Self::parse_xml_with_context(&xml_content, xml_path)
    }

    /// 解析 DDEX ERN XML 内容（向后兼容）
    pub fn parse_xml_static(xml_content: &str) -> Result<ParsedRelease, UmgError> {
        Self::parse_xml_with_context(xml_content, "")
    }

    /// 解析 DDEX ERN XML 内容（带上下文信息）
    pub fn parse_xml_with_context(xml_content: &str, xml_file_path: &str) -> Result<ParsedRelease, UmgError> {
        info!("开始解析 UMG DDEX ERN XML");

        // 从文件路径推导基础目录和 GRid
        let (base_dir, grid) = if !xml_file_path.is_empty() {
            let path = std::path::Path::new(xml_file_path);
            let base_dir = path.parent()
                .and_then(|p| p.to_str())
                .unwrap_or("")
                .to_string();

            // 从文件名提取 GRid（假设文件名格式为 {grid}.xml）
            let grid = path.file_stem()
                .and_then(|s| s.to_str())
                .unwrap_or("")
                .to_string();

            debug!("推导出基础目录: {}, GRid: {}", base_dir, grid);
            (base_dir, grid)
        } else {
            (String::new(), String::new())
        };

        // 解析 XML 文档
        let doc = Document::parse(xml_content)
            .map_err(|e| UmgError::XmlParseError(format!("XML 解析失败: {}", e)))?;

        let root = doc.root_element();

        // 验证根元素
        if !root.has_tag_name("NewReleaseMessage") {
            return Err(UmgError::XmlParseError(
                "不是有效的 DDEX ERN NewReleaseMessage".to_string()
            ));
        }

        debug!("XML 根元素验证通过");

        // 解析各个部分
        let message_header = Self::parse_message_header(&root)?;
        let party_list = Self::parse_party_list(&root)?;
        let (audio_resources, image_resources, mut track_infos) = Self::parse_resource_list_with_context(&root, &base_dir, &grid)?;
        let album_info = Self::parse_album_info(&root)?;

        // 解析 TrackRelease 信息（UMG 特有）
        Self::parse_track_releases(&root, &mut track_infos, &party_list)?;
        
        let parsed_release = ParsedRelease {
            message_header,
            album_info,
            tracks: track_infos,
            audio_resources,
            image_resources,
            party_list,
        };
        
        info!("UMG DDEX ERN XML 解析完成，专辑: {}, 曲目数: {}", 
              parsed_release.album_info.title, 
              parsed_release.tracks.len());

        Ok(parsed_release)
    }

    /// 解析消息头信息
    fn parse_message_header(root: &Node) -> Result<MessageHeader, UmgError> {
        debug!("解析消息头信息");

        let header = root
            .children()
            .find(|n| n.has_tag_name("MessageHeader"))
            .ok_or_else(|| UmgError::XmlParseError("未找到 MessageHeader".to_string()))?;

        let message_thread_id = Self::get_text_content(&header, "MessageThreadId")?;
        let message_id = Self::get_text_content(&header, "MessageId")?;
        let created_datetime = Self::get_text_content(&header, "MessageCreatedDateTime")?;
        let control_type = Self::get_text_content(&header, "MessageControlType")?;

        // 解析发送方和接收方信息
        let sender = Self::parse_party_from_header(&header, "MessageSender")?;
        let recipient = Self::parse_party_from_header(&header, "MessageRecipient")?;

        Ok(MessageHeader {
            message_thread_id,
            message_id,
            sender,
            recipient,
            created_datetime,
            control_type,
        })
    }

    /// 从消息头解析参与方信息
    fn parse_party_from_header(header: &Node, party_type: &str) -> Result<PartyInfo, UmgError> {
        let party_node = header
            .children()
            .find(|n| n.has_tag_name(party_type))
            .ok_or_else(|| UmgError::XmlParseError(format!("未找到 {}", party_type)))?;

        let party_id = Self::get_text_content(&party_node, "PartyId")?;
        let party_name = Self::get_text_content(&party_node, "PartyName")
            .unwrap_or_else(|_| party_id.clone());

        Ok(PartyInfo {
            party_id,
            party_name,
            party_type: party_type.to_string(),
            roles: vec![],
        })
    }

    /// 解析 PartyList（UMG 特有）
    fn parse_party_list(root: &Node) -> Result<Vec<PartyInfo>, UmgError> {
        debug!("解析 PartyList");

        let party_list = root
            .children()
            .find(|n| n.has_tag_name("PartyList"));

        if party_list.is_none() {
            warn!("未找到 PartyList，使用空列表");
            return Ok(vec![]);
        }

        let party_list = party_list.unwrap();
        let mut parties = Vec::new();

        for party_node in party_list.children().filter(|n| n.has_tag_name("Party")) {
            let party_reference = party_node.attribute("PartyReference")
                .unwrap_or("")
                .to_string();

            let party_id = Self::get_text_content(&party_node, "PartyId")
                .unwrap_or_else(|_| party_reference.clone());

            let party_name = Self::get_text_content(&party_node, "PartyName")
                .unwrap_or_else(|_| party_id.clone());

            // 解析角色
            let mut roles = Vec::new();
            for role_node in party_node.children().filter(|n| n.has_tag_name("PartyRole")) {
                if let Ok(role) = Self::get_text_content(&role_node, "") {
                    roles.push(role);
                }
            }

            parties.push(PartyInfo {
                party_id,
                party_name,
                party_type: "Party".to_string(),
                roles,
            });
        }

        debug!("解析到 {} 个参与方", parties.len());
        Ok(parties)
    }

    /// 解析资源列表
    fn parse_resource_list_with_context(
        root: &Node,
        base_dir: &str,
        _grid: &str,
    ) -> Result<(Vec<AudioResource>, Vec<ImageResource>, Vec<TrackInfo>), UmgError> {
        debug!("解析资源列表");

        let resource_list = root
            .children()
            .find(|n| n.has_tag_name("ResourceList"))
            .ok_or_else(|| UmgError::XmlParseError("未找到 ResourceList".to_string()))?;

        let mut audio_resources = Vec::new();
        let mut image_resources = Vec::new();
        let mut track_infos = Vec::new();

        // 解析音频资源
        for sound_recording in resource_list.children().filter(|n| n.has_tag_name("SoundRecording")) {
            let resource_id = sound_recording.attribute("ResourceReference")
                .unwrap_or("")
                .to_string();

            // 解析 ISRC（UMG 使用 ResourceId/ISRC）
            let isrc = Self::get_text_content(&sound_recording, "ResourceId/ISRC")
                .unwrap_or_else(|_| String::new());

            // 解析标题
            let title = Self::get_text_content(&sound_recording, "DisplayTitle")
                .or_else(|_| Self::get_text_content(&sound_recording, "ReferenceTitle"))
                .unwrap_or_else(|_| format!("Track {}", resource_id));

            // 解析技术详情
            let mut duration = 0i64;
            let mut file_name = String::new();
            let mut file_path = String::new();
            let mut md5_hash = String::new();
            let mut file_size = 0u64;
            let mut bit_rate = None;
            let mut sample_rate = None;

            for tech_details in sound_recording.children().filter(|n| n.has_tag_name("TechnicalDetails")) {
                // 解析时长
                if let Ok(duration_str) = Self::get_text_content(&tech_details, "Duration") {
                    duration = Self::parse_duration(&duration_str);
                }

                // 解析文件信息
                if let Some(file_node) = tech_details.children().find(|n| n.has_tag_name("File")) {
                    file_name = Self::get_text_content(&file_node, "FileName")
                        .unwrap_or_else(|_| format!("{}.mp3", resource_id));
                    
                    file_path = if base_dir.is_empty() {
                        file_name.clone()
                    } else {
                        format!("{}/{}", base_dir, file_name)
                    };

                    md5_hash = Self::get_text_content(&file_node, "HashSum")
                        .unwrap_or_else(|_| String::new());

                    if let Ok(size_str) = Self::get_text_content(&file_node, "FileSize") {
                        file_size = size_str.parse().unwrap_or(0);
                    }
                }

                // 解析音频参数
                if let Ok(br) = Self::get_text_content(&tech_details, "BitRate") {
                    bit_rate = br.parse().ok();
                }

                if let Ok(sr) = Self::get_text_content(&tech_details, "SamplingRate") {
                    sample_rate = Some(sr);
                }
            }

            // 创建音频资源
            audio_resources.push(AudioResource {
                resource_id: resource_id.clone(),
                file_name,
                file_path,
                md5_hash,
                file_size,
                bit_rate,
                sample_rate,
                duration,
            });

            // 创建基础曲目信息（后续会在 parse_track_releases 中完善）
            track_infos.push(TrackInfo {
                resource_reference: resource_id,
                isrc,
                title,
                display_title: None,
                duration,
                track_number: 1,
                disc_number: 1,
                artists: vec![],
                contributors: vec![],
                language: None,
                genre: None,
                p_line: None,
                is_explicit: false,
                start_date: None,
                end_date: None,
            });
        }

        // 解析图片资源
        for image in resource_list.children().filter(|n| n.has_tag_name("Image")) {
            if let Ok(image_resource) = Self::parse_image_resource(&image, base_dir) {
                image_resources.push(image_resource);
            }
        }

        debug!("解析到 {} 个音频资源，{} 个图片资源，{} 个基础曲目", 
               audio_resources.len(), image_resources.len(), track_infos.len());

        Ok((audio_resources, image_resources, track_infos))
    }

    /// 获取文本内容的辅助方法
    fn get_text_content(node: &Node, path: &str) -> Result<String, UmgError> {
        if path.is_empty() {
            return Ok(node.text().unwrap_or("").to_string());
        }

        let parts: Vec<&str> = path.split('/').collect();
        let mut current = *node;

        for part in parts {
            current = current
                .children()
                .find(|n| n.has_tag_name(part))
                .ok_or_else(|| UmgError::XmlParseError(format!("未找到节点: {}", part)))?;
        }

        Ok(current.text().unwrap_or("").to_string())
    }

    /// 解析时长字符串（PT3M45S 格式）
    fn parse_duration(duration_str: &str) -> i64 {
        // 简化的时长解析，支持 PT3M45S 格式
        if duration_str.starts_with("PT") && duration_str.ends_with("S") {
            let content = &duration_str[2..duration_str.len()-1];
            let mut total_seconds = 0i64;

            if let Some(m_pos) = content.find('M') {
                if let Ok(minutes) = content[..m_pos].parse::<i64>() {
                    total_seconds += minutes * 60;
                }
                if let Ok(seconds) = content[m_pos+1..].parse::<i64>() {
                    total_seconds += seconds;
                }
            } else if let Ok(seconds) = content.parse::<i64>() {
                total_seconds = seconds;
            }

            total_seconds
        } else {
            0
        }
    }

    /// 解析图片资源
    fn parse_image_resource(image_node: &Node, base_dir: &str) -> Result<ImageResource, UmgError> {
        let resource_id = image_node.attribute("ResourceReference")
            .unwrap_or("")
            .to_string();

        let image_type = Self::get_text_content(image_node, "ImageType")
            .unwrap_or_else(|_| "Unknown".to_string());

        // 解析技术详情
        let tech_details = image_node
            .children()
            .find(|n| n.has_tag_name("ImageDetailsByTerritory"))
            .and_then(|n| n.children().find(|n| n.has_tag_name("TechnicalImageDetails")))
            .ok_or_else(|| UmgError::XmlParseError("未找到图片技术详情".to_string()))?;

        // 解析文件信息
        let file_node = tech_details
            .children()
            .find(|n| n.has_tag_name("File"))
            .ok_or_else(|| UmgError::XmlParseError("未找到 File 节点".to_string()))?;

        let file_name = Self::get_text_content(&file_node, "FileName")
            .unwrap_or_else(|_| format!("{}.jpg", resource_id));

        let file_path = if base_dir.is_empty() {
            file_name.clone()
        } else {
            format!("{}/{}", base_dir, file_name)
        };

        let md5_hash = Self::get_text_content(&file_node, "HashSum")
            .unwrap_or_else(|_| String::new());

        let file_size = Self::get_text_content(&file_node, "FileSize")
            .ok()
            .and_then(|s| s.parse().ok())
            .unwrap_or(0);

        // 解析图片参数
        let codec_type = Self::get_text_content(&tech_details, "ImageCodecType")
            .unwrap_or_else(|_| "Unknown".to_string());
        let height = Self::get_text_content(&tech_details, "ImageHeight")
            .ok()
            .and_then(|s| s.parse().ok());
        let width = Self::get_text_content(&tech_details, "ImageWidth")
            .ok()
            .and_then(|s| s.parse().ok());
        let resolution = Self::get_text_content(&tech_details, "ImageResolution")
            .ok()
            .and_then(|s| s.parse().ok());

        Ok(ImageResource {
            resource_id,
            image_type,
            file_name,
            file_path,
            md5_hash,
            file_size,
            width,
            height,
            resolution,
            codec_type,
        })
    }

    /// 解析专辑信息
    fn parse_album_info(root: &Node) -> Result<AlbumInfo, UmgError> {
        debug!("解析专辑信息");

        let release_list = root
            .children()
            .find(|n| n.has_tag_name("ReleaseList"))
            .ok_or_else(|| UmgError::XmlParseError("未找到 ReleaseList".to_string()))?;

        // 查找主发行版本
        let main_release = release_list
            .children()
            .filter(|n| n.has_tag_name("Release"))
            .find(|n| {
                n.attribute("IsMainRelease")
                    .map(|attr| attr == "true")
                    .unwrap_or(false)
            })
            .or_else(|| release_list.children().find(|n| n.has_tag_name("Release")))
            .ok_or_else(|| UmgError::XmlParseError("未找到 Release".to_string()))?;

        // 解析发行 ID
        let release_id = main_release
            .children()
            .find(|n| n.has_tag_name("ReleaseId"))
            .ok_or_else(|| UmgError::XmlParseError("未找到 ReleaseId".to_string()))?;

        let grid = Self::get_text_content(&release_id, "GRid")?;
        let icpn = Self::get_text_content(&release_id, "ICPN")
            .unwrap_or_else(|_| String::new());

        // 解析发行类型
        let release_type = Self::get_text_content(&main_release, "ReleaseType")
            .unwrap_or_else(|_| "Album".to_string());

        // 解析标题
        let title = Self::get_text_content(&main_release, "ReferenceTitle")?;

        // 解析发行详情
        let mut release_date = String::new();
        let mut display_artist = String::new();
        let mut formal_title = None;
        let mut label_name = String::new();

        for details in main_release.children().filter(|n| n.has_tag_name("ReleaseDetailsByTerritory")) {
            if release_date.is_empty() {
                release_date = Self::get_text_content(&details, "ReleaseDate")
                    .unwrap_or_else(|_| String::new());
            }

            if display_artist.is_empty() {
                display_artist = Self::get_text_content(&details, "DisplayArtistName")
                    .unwrap_or_else(|_| String::new());
            }

            if label_name.is_empty() {
                label_name = Self::get_text_content(&details, "LabelName")
                    .unwrap_or_else(|_| String::new());
            }

            // 解析正式标题
            if formal_title.is_none() {
                for title_node in details.children().filter(|n| n.has_tag_name("Title")) {
                    if let Ok(title_text) = Self::get_text_content(&title_node, "TitleText") {
                        formal_title = Some(title_text);
                        break;
                    }
                }
            }
        }

        Ok(AlbumInfo {
            title: formal_title.unwrap_or(title),
            formal_title: None,
            grid,
            icpn,
            release_type,
            release_date,
            duration: 0, // 需要从曲目中计算
            copyright: String::new(), // UMG 可能在其他地方
            display_artist,
            label_name,
        })
    }

    /// 解析 TrackRelease 信息（UMG 特有）
    fn parse_track_releases(
        root: &Node,
        track_infos: &mut Vec<TrackInfo>,
        party_list: &[PartyInfo],
    ) -> Result<(), UmgError> {
        debug!("解析 TrackRelease 信息");

        let release_list = root
            .children()
            .find(|n| n.has_tag_name("ReleaseList"))
            .ok_or_else(|| UmgError::XmlParseError("未找到 ReleaseList".to_string()))?;

        // 创建资源引用到曲目的映射
        let mut resource_to_track: HashMap<String, usize> = HashMap::new();
        for (index, track) in track_infos.iter().enumerate() {
            resource_to_track.insert(track.resource_reference.clone(), index);
        }

        // 解析 TrackRelease
        for track_release in release_list.children().filter(|n| n.has_tag_name("TrackRelease")) {
            let resource_reference = Self::get_text_content(&track_release, "ResourceReference")
                .unwrap_or_else(|_| String::new());

            if let Some(&track_index) = resource_to_track.get(&resource_reference) {
                let track = &mut track_infos[track_index];

                // 解析曲目和碟片序号
                if let Ok(track_num_str) = Self::get_text_content(&track_release, "TrackNumber") {
                    track.track_number = track_num_str.parse().unwrap_or(1);
                }

                if let Ok(disc_num_str) = Self::get_text_content(&track_release, "DiscNumber") {
                    track.disc_number = disc_num_str.parse().unwrap_or(1);
                }

                // 解析开始和结束日期
                track.start_date = Self::get_text_content(&track_release, "StartDate").ok();
                track.end_date = Self::get_text_content(&track_release, "EndDate").ok();

                // 解析艺术家信息
                for artist_node in track_release.children().filter(|n| n.has_tag_name("DisplayArtist")) {
                    if let Ok(artist) = Self::parse_artist_info(&artist_node, party_list) {
                        track.artists.push(artist);
                    }
                }

                // 解析贡献者信息
                for contributor_node in track_release.children().filter(|n| n.has_tag_name("Contributor")) {
                    if let Ok(contributor) = Self::parse_contributor_info(&contributor_node, party_list) {
                        track.contributors.push(contributor);
                    }
                }

                // 解析其他信息
                track.p_line = Self::get_text_content(&track_release, "PLine/PLineText").ok();

                if let Ok(explicit_str) = Self::get_text_content(&track_release, "ParentalWarningType") {
                    track.is_explicit = explicit_str != "NoAdviceAvailable" && explicit_str != "NotExplicit";
                }
            }
        }

        Ok(())
    }

    /// 解析艺术家信息
    fn parse_artist_info(artist_node: &Node, party_list: &[PartyInfo]) -> Result<ArtistInfo, UmgError> {
        let name = Self::get_text_content(artist_node, "ArtistName")
            .or_else(|_| Self::get_text_content(artist_node, "PartyName"))?;

        let party_reference = artist_node.attribute("PartyReference")
            .map(|s| s.to_string());

        // 解析角色
        let mut roles = Vec::new();
        for role_node in artist_node.children().filter(|n| n.has_tag_name("ArtistRole")) {
            if let Ok(role) = Self::get_text_content(&role_node, "") {
                roles.push(role);
            }
        }

        Ok(ArtistInfo {
            name,
            roles,
            sequence_number: None,
            language_code: None,
            party_reference,
        })
    }

    /// 解析贡献者信息
    fn parse_contributor_info(contributor_node: &Node, _party_list: &[PartyInfo]) -> Result<ContributorInfo, UmgError> {
        let name = Self::get_text_content(contributor_node, "ContributorName")
            .or_else(|_| Self::get_text_content(contributor_node, "PartyName"))?;

        let role = Self::get_text_content(contributor_node, "ContributorRole")
            .unwrap_or_else(|_| "Contributor".to_string());

        let party_reference = contributor_node.attribute("PartyReference")
            .map(|s| s.to_string());

        Ok(ContributorInfo {
            name,
            role,
            party_reference,
        })
    }
}
