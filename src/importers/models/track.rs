//! 曲目相关数据模型

use serde::{Deserialize, Serialize};

/// 曲目信息（标准化）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrackInfo {
    /// 曲目标题
    pub title: String,
    /// 艺术家名称
    pub artist_name: String,
    /// 曲目序号
    pub track_number: u32,
    /// 碟片序号
    pub disc_number: Option<u32>,
    /// 时长（秒）
    pub duration_seconds: u32,
    /// ISRC 码
    pub isrc: Option<String>,
    /// 流派
    pub genre: Option<String>,
    /// 作曲者
    pub composer: Option<String>,
    /// 作词者
    pub lyricist: Option<String>,
    /// 制作人
    pub producer: Option<String>,
    /// 版权信息
    pub copyright: Option<String>,
    /// 关联的音频资源 ID
    pub audio_resource_id: Option<String>,
    /// 提供商特定的曲目信息
    pub provider_metadata: std::collections::HashMap<String, String>,
}

impl TrackInfo {
    /// 创建新的曲目信息
    pub fn new(title: String, artist_name: String, track_number: u32) -> Self {
        Self {
            title,
            artist_name,
            track_number,
            disc_number: None,
            duration_seconds: 0,
            isrc: None,
            genre: None,
            composer: None,
            lyricist: None,
            producer: None,
            copyright: None,
            audio_resource_id: None,
            provider_metadata: std::collections::HashMap::new(),
        }
    }
    
    /// 设置时长
    pub fn with_duration(mut self, duration_seconds: u32) -> Self {
        self.duration_seconds = duration_seconds;
        self
    }
    
    /// 设置 ISRC
    pub fn with_isrc(mut self, isrc: String) -> Self {
        self.isrc = Some(isrc);
        self
    }
    
    /// 设置关联的音频资源 ID
    pub fn with_audio_resource(mut self, resource_id: String) -> Self {
        self.audio_resource_id = Some(resource_id);
        self
    }
    
    /// 添加提供商特定的元数据
    pub fn add_provider_metadata(mut self, key: String, value: String) -> Self {
        self.provider_metadata.insert(key, value);
        self
    }
    
    /// 验证曲目信息的完整性
    pub fn validate(&self) -> Result<(), String> {
        if self.title.is_empty() {
            return Err("Track title cannot be empty".to_string());
        }
        
        if self.artist_name.is_empty() {
            return Err("Artist name cannot be empty".to_string());
        }
        
        if self.track_number == 0 {
            return Err("Track number must be greater than 0".to_string());
        }
        
        // 验证 ISRC 格式（如果存在）
        if let Some(ref isrc) = self.isrc {
            if !is_valid_isrc(isrc) {
                return Err(format!("Invalid ISRC format: {}", isrc));
            }
        }
        
        Ok(())
    }
}

/// 验证 ISRC 格式
fn is_valid_isrc(isrc: &str) -> bool {
    // ISRC 格式：CC-XXX-YY-NNNNN
    // 例如：US-S1Z-99-00001
    // 总长度应该是 15 位（包括连字符）
    if isrc.len() != 15 {
        return false;
    }

    let chars: Vec<char> = isrc.chars().collect();

    // 前两位应该是字母（国家代码）
    if !chars[0].is_ascii_alphabetic() || !chars[1].is_ascii_alphabetic() {
        return false;
    }

    // 第3位应该是连字符
    if chars[2] != '-' {
        return false;
    }

    // 第4-6位应该是字母或数字（注册商代码）
    for i in 3..6 {
        if !chars[i].is_ascii_alphanumeric() {
            return false;
        }
    }

    // 第7位应该是连字符
    if chars[6] != '-' {
        return false;
    }

    // 第8-9位应该是数字（年份）
    if !chars[7].is_ascii_digit() || !chars[8].is_ascii_digit() {
        return false;
    }

    // 第10位应该是连字符
    if chars[9] != '-' {
        return false;
    }

    // 第11-15位应该是数字（序列号）
    for i in 10..15 {
        if !chars[i].is_ascii_digit() {
            return false;
        }
    }

    true
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_track_info_creation() {
        let track = TrackInfo::new("Test Song".to_string(), "Test Artist".to_string(), 1)
            .with_duration(180)
            .with_isrc("US-S1Z-99-00001".to_string());
        
        assert_eq!(track.title, "Test Song");
        assert_eq!(track.artist_name, "Test Artist");
        assert_eq!(track.track_number, 1);
        assert_eq!(track.duration_seconds, 180);
        assert_eq!(track.isrc, Some("US-S1Z-99-00001".to_string()));
    }
    
    #[test]
    fn test_track_validation() {
        let valid_track = TrackInfo::new("Test Song".to_string(), "Test Artist".to_string(), 1);
        assert!(valid_track.validate().is_ok());
        
        let invalid_track = TrackInfo::new("".to_string(), "Test Artist".to_string(), 1);
        assert!(invalid_track.validate().is_err());
    }
    
    #[test]
    fn test_isrc_validation() {
        assert!(is_valid_isrc("US-S1Z-99-00001"));
        assert!(is_valid_isrc("GB-ABC-12-34567"));
        assert!(!is_valid_isrc("US-S1Z-99-0001")); // 太短
        assert!(!is_valid_isrc("123-S1Z-99-00001")); // 国家代码不是字母
        assert!(!is_valid_isrc("US_S1Z_99_00001")); // 分隔符错误
    }
}
