//! 资源相关数据模型

use serde::{Deserialize, Serialize};
use std::path::Path;

/// 音频资源信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioResource {
    /// 资源 ID
    pub resource_id: String,
    /// 本地文件路径
    pub local_path: String,
    /// 远程文件路径（NAS 上的路径）
    pub remote_path: Option<String>,
    /// 文件名
    pub file_name: String,
    /// 文件大小（字节）
    pub file_size: u64,
    /// 文件格式
    pub format: AudioFormat,
    /// 比特率
    pub bitrate: Option<u32>,
    /// 采样率
    pub sample_rate: Option<u32>,
    /// 声道数
    pub channels: Option<u8>,
    /// 时长（秒）
    pub duration_seconds: Option<u32>,
    /// 文件校验和
    pub checksum: Option<String>,
    /// 是否已上传
    pub uploaded: bool,
    /// 上传时间
    pub uploaded_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// 图片资源信息
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ImageResource {
    /// 资源 ID
    pub resource_id: String,
    /// 本地文件路径
    pub local_path: String,
    /// 远程文件路径（NAS 上的路径）
    pub remote_path: Option<String>,
    /// 文件名
    pub file_name: String,
    /// 文件大小（字节）
    pub file_size: u64,
    /// 图片类型
    pub image_type: ImageType,
    /// 图片格式
    pub format: ImageFormat,
    /// 宽度（像素）
    pub width: Option<u32>,
    /// 高度（像素）
    pub height: Option<u32>,
    /// 文件校验和
    pub checksum: Option<String>,
    /// 是否已上传
    pub uploaded: bool,
    /// 上传时间
    pub uploaded_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// 音频格式
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum AudioFormat {
    Mp3,
    Flac,
    Wav,
    Aac,
    Ogg,
    M4a,
    Unknown(String),
}

/// 图片类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ImageType {
    /// 专辑封面
    AlbumCover,
    /// 艺术家照片
    ArtistPhoto,
    /// 背景图片
    Background,
    /// 其他
    Other,
}

/// 图片格式
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ImageFormat {
    Jpeg,
    Png,
    Gif,
    Bmp,
    Webp,
    Unknown(String),
}

impl AudioResource {
    /// 从文件路径创建音频资源
    pub fn from_path(resource_id: String, local_path: String) -> Result<Self, String> {
        let path = Path::new(&local_path);
        
        if !path.exists() {
            return Err(format!("File does not exist: {}", local_path));
        }
        
        let file_name = path.file_name()
            .and_then(|n| n.to_str())
            .ok_or_else(|| "Invalid file name".to_string())?
            .to_string();
        
        let file_size = std::fs::metadata(&path)
            .map_err(|e| format!("Failed to get file metadata: {}", e))?
            .len();
        
        let format = AudioFormat::from_extension(
            path.extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("")
        );
        
        Ok(Self {
            resource_id,
            local_path,
            remote_path: None,
            file_name,
            file_size,
            format,
            bitrate: None,
            sample_rate: None,
            channels: None,
            duration_seconds: None,
            checksum: None,
            uploaded: false,
            uploaded_at: None,
        })
    }
    
    /// 标记为已上传
    pub fn mark_uploaded(&mut self, remote_path: String) {
        self.remote_path = Some(remote_path);
        self.uploaded = true;
        self.uploaded_at = Some(chrono::Utc::now());
    }
}

impl ImageResource {
    /// 从文件路径创建图片资源
    pub fn from_path(resource_id: String, local_path: String, image_type: ImageType) -> Result<Self, String> {
        let path = Path::new(&local_path);
        
        if !path.exists() {
            return Err(format!("File does not exist: {}", local_path));
        }
        
        let file_name = path.file_name()
            .and_then(|n| n.to_str())
            .ok_or_else(|| "Invalid file name".to_string())?
            .to_string();
        
        let file_size = std::fs::metadata(&path)
            .map_err(|e| format!("Failed to get file metadata: {}", e))?
            .len();
        
        let format = ImageFormat::from_extension(
            path.extension()
                .and_then(|ext| ext.to_str())
                .unwrap_or("")
        );
        
        Ok(Self {
            resource_id,
            local_path,
            remote_path: None,
            file_name,
            file_size,
            image_type,
            format,
            width: None,
            height: None,
            checksum: None,
            uploaded: false,
            uploaded_at: None,
        })
    }
    
    /// 标记为已上传
    pub fn mark_uploaded(&mut self, remote_path: String) {
        self.remote_path = Some(remote_path);
        self.uploaded = true;
        self.uploaded_at = Some(chrono::Utc::now());
    }
}

impl AudioFormat {
    /// 从文件扩展名推断格式
    pub fn from_extension(ext: &str) -> Self {
        match ext.to_lowercase().as_str() {
            "mp3" => AudioFormat::Mp3,
            "flac" => AudioFormat::Flac,
            "wav" => AudioFormat::Wav,
            "aac" => AudioFormat::Aac,
            "ogg" => AudioFormat::Ogg,
            "m4a" => AudioFormat::M4a,
            _ => AudioFormat::Unknown(ext.to_string()),
        }
    }
    
    /// 获取文件扩展名
    pub fn extension(&self) -> &str {
        match self {
            AudioFormat::Mp3 => "mp3",
            AudioFormat::Flac => "flac",
            AudioFormat::Wav => "wav",
            AudioFormat::Aac => "aac",
            AudioFormat::Ogg => "ogg",
            AudioFormat::M4a => "m4a",
            AudioFormat::Unknown(ext) => ext,
        }
    }
}

impl ImageFormat {
    /// 从文件扩展名推断格式
    pub fn from_extension(ext: &str) -> Self {
        match ext.to_lowercase().as_str() {
            "jpg" | "jpeg" => ImageFormat::Jpeg,
            "png" => ImageFormat::Png,
            "gif" => ImageFormat::Gif,
            "bmp" => ImageFormat::Bmp,
            "webp" => ImageFormat::Webp,
            _ => ImageFormat::Unknown(ext.to_string()),
        }
    }
    
    /// 获取文件扩展名
    pub fn extension(&self) -> &str {
        match self {
            ImageFormat::Jpeg => "jpg",
            ImageFormat::Png => "png",
            ImageFormat::Gif => "gif",
            ImageFormat::Bmp => "bmp",
            ImageFormat::Webp => "webp",
            ImageFormat::Unknown(ext) => ext,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_audio_format_from_extension() {
        assert_eq!(AudioFormat::from_extension("mp3"), AudioFormat::Mp3);
        assert_eq!(AudioFormat::from_extension("MP3"), AudioFormat::Mp3);
        assert_eq!(AudioFormat::from_extension("flac"), AudioFormat::Flac);
        assert_eq!(AudioFormat::from_extension("unknown"), AudioFormat::Unknown("unknown".to_string()));
    }
    
    #[test]
    fn test_image_format_from_extension() {
        assert_eq!(ImageFormat::from_extension("jpg"), ImageFormat::Jpeg);
        assert_eq!(ImageFormat::from_extension("jpeg"), ImageFormat::Jpeg);
        assert_eq!(ImageFormat::from_extension("PNG"), ImageFormat::Png);
        assert_eq!(ImageFormat::from_extension("unknown"), ImageFormat::Unknown("unknown".to_string()));
    }
}
