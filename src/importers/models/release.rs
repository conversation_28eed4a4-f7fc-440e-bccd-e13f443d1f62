//! 发行版本相关数据模型

use serde::{Deserialize, Serialize};
use super::{TrackInfo, AudioResource, ImageResource, MessageHeader, AlbumInfo, ProviderInfo};

/// 发行版本的完整信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Release {
    /// 发行版本 ID
    pub release_id: String,
    /// 消息头信息
    pub message_header: MessageHeader,
    /// 专辑信息
    pub album_info: AlbumInfo,
    /// 曲目列表
    pub tracks: Vec<TrackInfo>,
    /// 音频资源
    pub audio_resources: Vec<AudioResource>,
    /// 图片资源
    pub image_resources: Vec<ImageResource>,
    /// 提供商信息
    pub provider_info: ProviderInfo,
    /// 发行版本状态
    pub status: ReleaseStatus,
}

/// 发行版本状态
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ReleaseStatus {
    /// 新建
    New,
    /// 解析中
    Parsing,
    /// 解析完成
    Parsed,
    /// 上传中
    Uploading,
    /// 上传完成
    Uploaded,
    /// 数据库保存中
    Saving,
    /// 完成
    Complete,
    /// 失败
    Failed(String),
}

impl Release {
    /// 创建新的发行版本
    pub fn new(
        release_id: String,
        message_header: MessageHeader,
        album_info: AlbumInfo,
        provider_info: ProviderInfo,
    ) -> Self {
        Self {
            release_id,
            message_header,
            album_info,
            tracks: Vec::new(),
            audio_resources: Vec::new(),
            image_resources: Vec::new(),
            provider_info,
            status: ReleaseStatus::New,
        }
    }
    
    /// 添加曲目
    pub fn add_track(&mut self, track: TrackInfo) {
        self.tracks.push(track);
    }
    
    /// 添加音频资源
    pub fn add_audio_resource(&mut self, resource: AudioResource) {
        self.audio_resources.push(resource);
    }
    
    /// 添加图片资源
    pub fn add_image_resource(&mut self, resource: ImageResource) {
        self.image_resources.push(resource);
    }
    
    /// 设置状态
    pub fn set_status(&mut self, status: ReleaseStatus) {
        self.status = status;
    }
    
    /// 获取曲目数量
    pub fn track_count(&self) -> usize {
        self.tracks.len()
    }
    
    /// 获取音频资源数量
    pub fn audio_resource_count(&self) -> usize {
        self.audio_resources.len()
    }
    
    /// 获取图片资源数量
    pub fn image_resource_count(&self) -> usize {
        self.image_resources.len()
    }
    
    /// 获取所有未上传的音频资源
    pub fn get_pending_audio_resources(&self) -> Vec<&AudioResource> {
        self.audio_resources.iter()
            .filter(|r| !r.uploaded)
            .collect()
    }
    
    /// 获取所有未上传的图片资源
    pub fn get_pending_image_resources(&self) -> Vec<&ImageResource> {
        self.image_resources.iter()
            .filter(|r| !r.uploaded)
            .collect()
    }
    
    /// 检查是否所有资源都已上传
    pub fn all_resources_uploaded(&self) -> bool {
        self.audio_resources.iter().all(|r| r.uploaded) &&
        self.image_resources.iter().all(|r| r.uploaded)
    }
    
    /// 验证发行版本的完整性
    pub fn validate(&self) -> Result<(), String> {
        // 验证基本信息
        if self.release_id.is_empty() {
            return Err("Release ID cannot be empty".to_string());
        }
        
        if self.album_info.title.is_empty() {
            return Err("Album title cannot be empty".to_string());
        }
        
        if self.album_info.artist_name.is_empty() {
            return Err("Artist name cannot be empty".to_string());
        }
        
        // 验证曲目信息
        if self.tracks.is_empty() {
            return Err("Release must have at least one track".to_string());
        }
        
        for (index, track) in self.tracks.iter().enumerate() {
            if let Err(e) = track.validate() {
                return Err(format!("Track {} validation failed: {}", index + 1, e));
            }
        }
        
        // 验证音频资源
        if self.audio_resources.is_empty() {
            return Err("Release must have at least one audio resource".to_string());
        }
        
        // 检查曲目和音频资源的对应关系
        let audio_resource_ids: std::collections::HashSet<_> = 
            self.audio_resources.iter().map(|r| &r.resource_id).collect();
        
        for track in &self.tracks {
            if let Some(ref resource_id) = track.audio_resource_id {
                if !audio_resource_ids.contains(resource_id) {
                    return Err(format!(
                        "Track '{}' references non-existent audio resource '{}'",
                        track.title, resource_id
                    ));
                }
            }
        }
        
        Ok(())
    }
    
    /// 生成发行版本摘要
    pub fn generate_summary(&self) -> ReleaseSummary {
        ReleaseSummary {
            release_id: self.release_id.clone(),
            album_title: self.album_info.title.clone(),
            artist_name: self.album_info.artist_name.clone(),
            track_count: self.tracks.len(),
            audio_resource_count: self.audio_resources.len(),
            image_resource_count: self.image_resources.len(),
            uploaded_audio_count: self.audio_resources.iter().filter(|r| r.uploaded).count(),
            uploaded_image_count: self.image_resources.iter().filter(|r| r.uploaded).count(),
            status: self.status.clone(),
            provider: self.provider_info.provider.clone(),
        }
    }
}

/// 发行版本摘要信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReleaseSummary {
    /// 发行版本 ID
    pub release_id: String,
    /// 专辑标题
    pub album_title: String,
    /// 艺术家名称
    pub artist_name: String,
    /// 曲目数量
    pub track_count: usize,
    /// 音频资源数量
    pub audio_resource_count: usize,
    /// 图片资源数量
    pub image_resource_count: usize,
    /// 已上传音频数量
    pub uploaded_audio_count: usize,
    /// 已上传图片数量
    pub uploaded_image_count: usize,
    /// 状态
    pub status: ReleaseStatus,
    /// 提供商
    pub provider: String,
}

impl ReleaseSummary {
    /// 获取上传进度百分比
    pub fn upload_progress(&self) -> f64 {
        let total_resources = self.audio_resource_count + self.image_resource_count;
        if total_resources == 0 {
            return 100.0;
        }
        
        let uploaded_resources = self.uploaded_audio_count + self.uploaded_image_count;
        (uploaded_resources as f64 / total_resources as f64) * 100.0
    }
    
    /// 是否上传完成
    pub fn is_upload_complete(&self) -> bool {
        self.uploaded_audio_count == self.audio_resource_count &&
        self.uploaded_image_count == self.image_resource_count
    }
}

impl ReleaseStatus {
    /// 是否为最终状态
    pub fn is_final(&self) -> bool {
        matches!(self, ReleaseStatus::Complete | ReleaseStatus::Failed(_))
    }
    
    /// 是否为成功状态
    pub fn is_success(&self) -> bool {
        matches!(self, ReleaseStatus::Complete)
    }
    
    /// 是否为失败状态
    pub fn is_failed(&self) -> bool {
        matches!(self, ReleaseStatus::Failed(_))
    }
    
    /// 获取状态描述
    pub fn description(&self) -> &str {
        match self {
            ReleaseStatus::New => "新建",
            ReleaseStatus::Parsing => "解析中",
            ReleaseStatus::Parsed => "解析完成",
            ReleaseStatus::Uploading => "上传中",
            ReleaseStatus::Uploaded => "上传完成",
            ReleaseStatus::Saving => "数据库保存中",
            ReleaseStatus::Complete => "完成",
            ReleaseStatus::Failed(_) => "失败",
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::importers::models::{PartyInfo, ProviderInfo};
    use chrono::Utc;
    
    #[test]
    fn test_release_creation() {
        let message_header = MessageHeader {
            message_thread_id: "test_thread".to_string(),
            message_id: "test_message".to_string(),
            sender: PartyInfo {
                party_id: "sender".to_string(),
                party_name: "Sender".to_string(),
                party_type: "Label".to_string(),
            },
            recipient: PartyInfo {
                party_id: "recipient".to_string(),
                party_name: "Recipient".to_string(),
                party_type: "DSP".to_string(),
            },
            created_datetime: "2023-01-01T00:00:00Z".to_string(),
            control_type: "NewReleaseMessage".to_string(),
        };
        
        let album_info = AlbumInfo {
            title: "Test Album".to_string(),
            artist_name: "Test Artist".to_string(),
            release_date: "2023-01-01".to_string(),
            album_type: "Album".to_string(),
            genre: "Pop".to_string(),
            label: "Test Label".to_string(),
            copyright: "2023 Test Label".to_string(),
            upc: Some("123456789012".to_string()),
            grid: None,
            description: None,
        };
        
        let provider_info = ProviderInfo {
            provider: "sony".to_string(),
            source_file: "/path/to/source.xml".to_string(),
            parsed_at: Utc::now(),
            metadata: std::collections::HashMap::new(),
        };
        
        let release = Release::new(
            "test_release".to_string(),
            message_header,
            album_info,
            provider_info,
        );
        
        assert_eq!(release.release_id, "test_release");
        assert_eq!(release.album_info.title, "Test Album");
        assert_eq!(release.status, ReleaseStatus::New);
        assert_eq!(release.track_count(), 0);
    }
}
