use anyhow::Result;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use rayon::prelude::*;
use crate::config::Config;
use crate::utils::grpc_client::GrpcFileClient;
use log::{info, error};

pub struct BaseImporter {
    pub config: Config,
    pub date: String,
    pub record: Arc<Mutex<HashMap<String, Vec<String>>>>,
    pub grpc_client: Option<GrpcFileClient>,
}

impl BaseImporter {
    pub fn new(config: Config, date: String) -> Self {
        let mut record = HashMap::new();
        record.insert("success".to_string(), Vec::new());
        record.insert("failure".to_string(), Vec::new());
        record.insert("update".to_string(), Vec::new());
        record.insert("missingXML".to_string(), Vec::new());
        
        Self {
            config,
            date,
            record: Arc::new(Mutex::new(record)),
            grpc_client: None,
        }
    }
    
    pub async fn initialize_grpc_client(&mut self) -> Result<()> {
        let grpc_config = self.config.grpc.as_ref()
            .ok_or_else(|| anyhow::anyhow!("gRPC configuration is required but not found"))?;

        info!("Initializing gRPC client for server: {}", grpc_config.server_addr);
        let client = GrpcFileClient::new(&grpc_config.server_addr).await?;
        self.grpc_client = Some(client);
        info!("gRPC client initialized successfully");
        Ok(())
    }
    
    pub async fn upload_file(&mut self, local_path: &str, remote_path: &str) -> Result<()> {
        let client = self.grpc_client.as_mut()
            .ok_or_else(|| anyhow::anyhow!("gRPC client not initialized"))?;

        info!("Uploading file via gRPC: {} -> {}", local_path, remote_path);
        client.upload_file_resumable(local_path, remote_path).await?;
        info!("File upload completed: {}", remote_path);
        Ok(())
    }
    
    pub fn get_directories(&self) -> Result<Vec<String>> {
        use walkdir::WalkDir;

        let root_path = &self.config.root_folder;
        let mut directories = Vec::new();

        // 扫描两级目录结构，例如: data/sony/20240901/N_A10301A0002866291X_20240829191115584
        for entry in WalkDir::new(root_path).min_depth(2).max_depth(2) {
            let entry = entry?;
            if entry.file_type().is_dir() {
                // 获取相对于根目录的完整路径
                if let Ok(relative_path) = entry.path().strip_prefix(root_path) {
                    if let Some(path_str) = relative_path.to_str() {
                        directories.push(path_str.to_string());
                    }
                }
            }
        }

        info!("🔍 扫描到 {} 个二级目录", directories.len());
        Ok(directories)
    }
    
    pub fn import_parallel<F, R>(&self, dirs: Vec<String>, process_fn: F) -> Result<Vec<R>>
    where
        F: Fn(&str) -> R + Sync + Send,
        R: Send,
    {
        info!("Starting parallel import with {} threads", self.config.thread_count);
        
        let pool = rayon::ThreadPoolBuilder::new()
            .num_threads(self.config.thread_count)
            .build()?;
        
        let results = pool.install(|| {
            dirs.par_iter()
                .map(|dir| process_fn(dir))
                .collect()
        });
        
        info!("Parallel import completed");
        Ok(results)
    }
    
    pub fn log(&self, message: &str, to_console: bool) {
        if to_console {
            println!("{}", message);
        }
        info!("{}", message);
    }
    
    pub fn send_email_report(&self, subject: &str, body: &str) -> Result<()> {
        use lettre::message::header::ContentType;
        use lettre::transport::smtp::authentication::Credentials;
        use lettre::{Message, SmtpTransport, Transport};
        
        let mail_config = &self.config.mail;
        
        for to_email in &mail_config.to_emails {
            let email = Message::builder()
                .from(mail_config.from_email.parse()?)
                .to(to_email.parse()?)
                .subject(subject)
                .header(ContentType::TEXT_PLAIN)
                .body(body.to_string())?;
            
            let creds = Credentials::new(
                mail_config.username.clone(),
                mail_config.password.clone(),
            );
            
            let mailer = SmtpTransport::relay(&mail_config.smtp_server)?
                .credentials(creds)
                .build();
            
            match mailer.send(&email) {
                Ok(_) => info!("Email sent successfully to {}", to_email),
                Err(e) => error!("Failed to send email to {}: {}", to_email, e),
            }
        }
        
        Ok(())
    }
}
