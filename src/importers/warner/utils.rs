//! Warner 工具函数和数据转换

use anyhow::Result;
use log::debug;

use crate::importers::models::{
    ParsedRelease as StandardParsedRelease,
    MessageHeader as StandardMessageHeader,
    AlbumInfo as StandardAlbumInfo,
    TrackInfo as StandardTrackInfo,
    AudioResource as StandardAudioResource,
    ImageResource as StandardImageResource,
    ProviderInfo,
    AudioFormat,
    ImageFormat,
    ImageType,
};

use super::{ParsedRelease, MessageHeader, AlbumInfo, TrackInfo, AudioResource, ImageResource, DealInfo};

/// 将 Warner 特定的解析结果转换为标准格式
pub fn convert_warner_to_standard(warner_parsed: ParsedRelease, xml_path: &str) -> Result<StandardParsedRelease> {
    debug!("转换 Warner 解析结果为标准格式");

    let standard_message_header = convert_message_header(&warner_parsed.message_header);
    let standard_album_info = convert_album_info(&warner_parsed.album_info);
    let standard_tracks = convert_tracks(&warner_parsed.tracks);
    let standard_audio_resources = convert_audio_resources(&warner_parsed.audio_resources);
    let standard_image_resources = convert_image_resources(&warner_parsed.image_resources);

    // 创建提供商信息
    let provider_info = ProviderInfo {
        provider: "Warner".to_string(),
        source_file: xml_path.to_string(),
        parsed_at: chrono::Utc::now(),
        metadata: create_warner_metadata(&warner_parsed.deal_list, &warner_parsed.update_indicator),
    };

    Ok(StandardParsedRelease {
        message_header: standard_message_header,
        album_info: standard_album_info,
        tracks: standard_tracks,
        audio_resources: standard_audio_resources,
        image_resources: standard_image_resources,
        provider_info,
    })
}

/// 转换消息头信息
fn convert_message_header(warner_header: &MessageHeader) -> StandardMessageHeader {
    StandardMessageHeader {
        message_thread_id: warner_header.message_thread_id.clone(),
        message_id: warner_header.message_id.clone(),
        sender: crate::importers::models::PartyInfo {
            party_id: warner_header.sender.party_id.clone(),
            party_name: warner_header.sender.party_name.clone(),
            party_type: warner_header.sender.party_type.clone(),
        },
        recipient: crate::importers::models::PartyInfo {
            party_id: warner_header.recipient.party_id.clone(),
            party_name: warner_header.recipient.party_name.clone(),
            party_type: warner_header.recipient.party_type.clone(),
        },
        created_datetime: warner_header.created_datetime.clone(),
        control_type: warner_header.control_type.clone(),
    }
}

/// 转换专辑信息
fn convert_album_info(warner_album: &AlbumInfo) -> StandardAlbumInfo {
    StandardAlbumInfo {
        title: warner_album.title.clone(),
        artist_name: warner_album.display_artist.clone(),
        release_date: warner_album.release_date.clone(),
        album_type: warner_album.release_type.clone(),
        genre: "Unknown".to_string(), // Warner 专辑级别可能没有流派信息
        label: warner_album.label_name.clone(),
        copyright: warner_album.copyright.clone(),
        upc: None, // Warner 可能没有 UPC，但有 ICPN
        grid: Some(warner_album.grid.clone()),
        description: warner_album.formal_title.clone(),
    }
}

/// 转换曲目信息
fn convert_tracks(warner_tracks: &[TrackInfo]) -> Vec<StandardTrackInfo> {
    warner_tracks.iter().map(|warner_track| {
        // 提取主要艺术家名称
        let artist_name = if !warner_track.artists.is_empty() {
            warner_track.artists[0].name.clone()
        } else {
            "Unknown Artist".to_string()
        };

        // 提取作曲者和作词者
        let mut composer = None;
        let mut lyricist = None;
        let mut producer = None;

        for contributor in &warner_track.contributors {
            match contributor.role.to_lowercase().as_str() {
                "composer" | "songwriter" => {
                    if composer.is_none() {
                        composer = Some(contributor.name.clone());
                    }
                }
                "lyricist" | "writer" => {
                    if lyricist.is_none() {
                        lyricist = Some(contributor.name.clone());
                    }
                }
                "producer" => {
                    if producer.is_none() {
                        producer = Some(contributor.name.clone());
                    }
                }
                _ => {}
            }
        }

        // 创建提供商特定的元数据
        let mut provider_metadata = std::collections::HashMap::new();
        provider_metadata.insert("resource_reference".to_string(), warner_track.resource_reference.clone());
        
        if let Some(ref reference_title) = warner_track.reference_title {
            provider_metadata.insert("reference_title".to_string(), reference_title.clone());
        }
        
        if let Some(ref p_line) = warner_track.p_line {
            provider_metadata.insert("p_line".to_string(), p_line.clone());
        }
        
        if let Some(ref label_name) = warner_track.label_name {
            provider_metadata.insert("label_name".to_string(), label_name.clone());
        }

        provider_metadata.insert("is_explicit".to_string(), warner_track.is_explicit.to_string());

        StandardTrackInfo {
            title: warner_track.title.clone(),
            artist_name,
            track_number: warner_track.track_number as u32,
            disc_number: Some(warner_track.disc_number as u32),
            duration_seconds: warner_track.duration as u32,
            isrc: if warner_track.isrc.is_empty() { None } else { Some(warner_track.isrc.clone()) },
            genre: warner_track.genre.clone(),
            composer,
            lyricist,
            producer,
            copyright: warner_track.p_line.clone(),
            audio_resource_id: Some(warner_track.resource_reference.clone()),
            provider_metadata,
        }
    }).collect()
}

/// 转换音频资源
fn convert_audio_resources(warner_resources: &[AudioResource]) -> Vec<StandardAudioResource> {
    warner_resources.iter().map(|warner_resource| {
        // 从文件名推断音频格式
        let format = match warner_resource.file_name.split('.').last().unwrap_or("").to_lowercase().as_str() {
            "mp3" => AudioFormat::Mp3,
            "flac" => AudioFormat::Flac,
            "wav" => AudioFormat::Wav,
            "aac" => AudioFormat::Aac,
            "m4a" => AudioFormat::M4a,
            "ogg" => AudioFormat::Ogg,
            ext => AudioFormat::Unknown(ext.to_string()),
        };

        StandardAudioResource {
            resource_id: warner_resource.resource_id.clone(),
            local_path: warner_resource.file_path.clone(),
            remote_path: None,
            file_name: warner_resource.file_name.clone(),
            file_size: warner_resource.file_size,
            format,
            bitrate: warner_resource.bit_rate,
            sample_rate: warner_resource.sample_rate.as_ref().and_then(|s| s.parse().ok()),
            channels: None,
            duration_seconds: Some(warner_resource.duration as u32),
            checksum: Some(warner_resource.md5_hash.clone()),
            uploaded: false,
            uploaded_at: None,
        }
    }).collect()
}

/// 转换图片资源
fn convert_image_resources(warner_resources: &[ImageResource]) -> Vec<StandardImageResource> {
    warner_resources.iter().map(|warner_resource| {
        // 从文件名推断图片格式
        let format = match warner_resource.file_name.split('.').last().unwrap_or("").to_lowercase().as_str() {
            "jpg" | "jpeg" => ImageFormat::Jpeg,
            "png" => ImageFormat::Png,
            "gif" => ImageFormat::Gif,
            "bmp" => ImageFormat::Bmp,
            "webp" => ImageFormat::Webp,
            ext => ImageFormat::Unknown(ext.to_string()),
        };

        // 从类型字符串推断图片类型
        let image_type = match warner_resource.image_type.to_lowercase().as_str() {
            "albumcover" | "cover" => ImageType::AlbumCover,
            "artistphoto" | "artist" => ImageType::ArtistPhoto,
            "background" => ImageType::Background,
            _ => ImageType::Other,
        };

        StandardImageResource {
            resource_id: warner_resource.resource_id.clone(),
            local_path: warner_resource.file_path.clone(),
            remote_path: None,
            file_name: warner_resource.file_name.clone(),
            file_size: warner_resource.file_size,
            image_type,
            format,
            width: warner_resource.width,
            height: warner_resource.height,
            checksum: Some(warner_resource.md5_hash.clone()),
            uploaded: false,
            uploaded_at: None,
        }
    }).collect()
}

/// 创建 Warner 特定的元数据
fn create_warner_metadata(deal_list: &[DealInfo], update_indicator: &str) -> std::collections::HashMap<String, String> {
    let mut metadata = std::collections::HashMap::new();
    
    metadata.insert("provider".to_string(), "Warner".to_string());
    metadata.insert("update_indicator".to_string(), update_indicator.to_string());
    metadata.insert("deal_count".to_string(), deal_list.len().to_string());
    
    // 添加主要交易信息
    for (index, deal) in deal_list.iter().enumerate() {
        let prefix = format!("deal_{}", index);
        metadata.insert(format!("{}_reference", prefix), deal.deal_reference.clone());
        metadata.insert(format!("{}_type", prefix), deal.deal_type.clone());
        metadata.insert(format!("{}_use_type", prefix), deal.use_type.clone());
        metadata.insert(format!("{}_territory", prefix), deal.territory_code.clone());
        metadata.insert(format!("{}_is_exclusive", prefix), deal.is_exclusive.to_string());
        metadata.insert(format!("{}_commercial_model", prefix), deal.commercial_model_type.clone());
        
        if let Some(ref start_date) = deal.start_date {
            metadata.insert(format!("{}_start_date", prefix), start_date.clone());
        }
        
        if let Some(ref end_date) = deal.end_date {
            metadata.insert(format!("{}_end_date", prefix), end_date.clone());
        }
    }
    
    metadata
}

/// 验证 Warner 解析结果的完整性
pub fn validate_warner_parsed_release(parsed: &ParsedRelease) -> Result<()> {
    // 验证基本信息
    if parsed.album_info.grid.is_empty() {
        return Err(anyhow::anyhow!("专辑 GRid 不能为空"));
    }
    
    if parsed.album_info.title.is_empty() {
        return Err(anyhow::anyhow!("专辑标题不能为空"));
    }
    
    // 验证曲目信息
    for (index, track) in parsed.tracks.iter().enumerate() {
        if track.title.is_empty() {
            return Err(anyhow::anyhow!("曲目 {} 标题不能为空", index + 1));
        }
        
        if track.resource_reference.is_empty() {
            return Err(anyhow::anyhow!("曲目 {} 资源引用不能为空", index + 1));
        }
        
        if track.track_number <= 0 {
            return Err(anyhow::anyhow!("曲目 {} 序号必须大于 0", index + 1));
        }
    }
    
    // 验证资源完整性
    let track_resource_refs: std::collections::HashSet<_> = parsed.tracks
        .iter()
        .map(|t| &t.resource_reference)
        .collect();
    
    let audio_resource_ids: std::collections::HashSet<_> = parsed.audio_resources
        .iter()
        .map(|r| &r.resource_id)
        .collect();
    
    for track_ref in &track_resource_refs {
        if !audio_resource_ids.contains(track_ref) {
            return Err(anyhow::anyhow!("曲目引用的音频资源 {} 不存在", track_ref));
        }
    }
    
    debug!("Warner 解析结果验证通过");
    Ok(())
}
