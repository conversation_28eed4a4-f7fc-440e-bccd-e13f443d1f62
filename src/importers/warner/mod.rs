//! Warner Music Group 导入器模块
//!
//! 这个模块提供了完整的 Warner DDEX ERN XML 解析和文件处理功能。
//!
//! # Warner 特点
//! - 需要处理 `DealList` 和 `ReleaseDeal` 信息
//! - 有复杂的交易和发行权限处理逻辑
//! - 类似 Sony 的基本结构但有特定的业务逻辑
//! - 支持多地区发行信息
//!
//! # 模块结构
//! - `ddex_parser`: Warner DDEX ERN XML 解析器
//! - `utils`: 工具函数和数据转换
//! - `importer`: 主要导入器实现

use serde::{Deserialize, Serialize};

// 导出子模块
pub mod ddex_parser;
pub mod utils;
pub mod importer;

// 重新导出主要类型
pub use ddex_parser::WarnerDdexParser;
pub use importer::WarnerImporter;
pub use utils::*;

/// Warner 解析错误类型
#[derive(Debug, thiserror::Error)]
pub enum WarnerError {
    #[error("XML 解析错误: {0}")]
    XmlParseError(String),
    
    #[error("IO 错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("数据验证错误: {0}")]
    ValidationError(String),
    
    #[error("缺少必需字段: {0}")]
    MissingFieldError(String),
    
    #[error("DealList 处理错误: {0}")]
    DealListError(String),
    
    #[error("ReleaseDeal 处理错误: {0}")]
    ReleaseDealError(String),
}

/// 解析后的发行版本信息（Warner 专用）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedRelease {
    /// 消息头信息
    pub message_header: MessageHeader,
    /// 专辑信息
    pub album_info: AlbumInfo,
    /// 曲目列表
    pub tracks: Vec<TrackInfo>,
    /// 音频资源
    pub audio_resources: Vec<AudioResource>,
    /// 图片资源
    pub image_resources: Vec<ImageResource>,
    /// Warner 特有的交易信息
    pub deal_list: Vec<DealInfo>,
    /// 更新指示器
    pub update_indicator: String,
}

/// 消息头信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageHeader {
    /// 消息线程 ID
    pub message_thread_id: String,
    /// 消息 ID
    pub message_id: String,
    /// 发送方信息
    pub sender: PartyInfo,
    /// 接收方信息
    pub recipient: PartyInfo,
    /// 创建时间
    pub created_datetime: String,
    /// 控制类型
    pub control_type: String,
}

/// 参与方信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PartyInfo {
    /// 参与方 ID
    pub party_id: String,
    /// 参与方名称
    pub party_name: String,
    /// 参与方类型
    pub party_type: String,
}

/// 专辑信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlbumInfo {
    /// 专辑标题
    pub title: String,
    /// 正式标题
    pub formal_title: Option<String>,
    /// GRid 标识符
    pub grid: String,
    /// ICPN 标识符
    pub icpn: String,
    /// 发行类型
    pub release_type: String,
    /// 发行日期
    pub release_date: String,
    /// 总时长（秒）
    pub duration: i64,
    /// 版权信息
    pub copyright: String,
    /// 显示艺术家
    pub display_artist: String,
    /// 唱片公司
    pub label_name: String,
    /// 发行年份
    pub year: i32,
    /// 发行月份
    pub month: i32,
}

/// 曲目信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrackInfo {
    /// 资源引用
    pub resource_reference: String,
    /// ISRC 标识符
    pub isrc: String,
    /// 标题
    pub title: String,
    /// 参考标题
    pub reference_title: Option<String>,
    /// 时长（秒）
    pub duration: i64,
    /// 曲目序号
    pub track_number: i32,
    /// 碟片序号
    pub disc_number: i32,
    /// 艺术家列表
    pub artists: Vec<ArtistInfo>,
    /// 贡献者列表
    pub contributors: Vec<ContributorInfo>,
    /// 语言
    pub language: Option<String>,
    /// 流派
    pub genre: Option<String>,
    /// 唱片公司
    pub label_name: Option<String>,
    /// P-Line 版权信息
    pub p_line: Option<String>,
    /// 是否显式内容
    pub is_explicit: bool,
}

/// 艺术家信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArtistInfo {
    /// 艺术家名称
    pub name: String,
    /// 角色列表
    pub roles: Vec<String>,
    /// 序号
    pub sequence_number: Option<i32>,
    /// 语言代码
    pub language_code: Option<String>,
}

/// 贡献者信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContributorInfo {
    /// 贡献者名称
    pub name: String,
    /// 角色
    pub role: String,
}

/// 音频资源信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioResource {
    /// 资源 ID
    pub resource_id: String,
    /// 文件名
    pub file_name: String,
    /// 文件路径
    pub file_path: String,
    /// MD5 哈希
    pub md5_hash: String,
    /// 文件大小
    pub file_size: u64,
    /// 比特率
    pub bit_rate: Option<u32>,
    /// 采样率
    pub sample_rate: Option<String>,
    /// 时长（秒）
    pub duration: i64,
}

/// 图片资源信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageResource {
    /// 资源 ID
    pub resource_id: String,
    /// 图片类型
    pub image_type: String,
    /// 文件名
    pub file_name: String,
    /// 文件路径
    pub file_path: String,
    /// MD5 哈希
    pub md5_hash: String,
    /// 文件大小
    pub file_size: u64,
    /// 图片宽度
    pub width: Option<u32>,
    /// 图片高度
    pub height: Option<u32>,
    /// 图片分辨率
    pub resolution: Option<u32>,
    /// 编解码器类型
    pub codec_type: String,
}

/// 交易信息（Warner 特有）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DealInfo {
    /// 交易引用
    pub deal_reference: String,
    /// 发行引用
    pub deal_release_reference: String,
    /// 交易类型
    pub deal_type: String,
    /// 使用类型
    pub use_type: String,
    /// 地区代码
    pub territory_code: String,
    /// 开始日期
    pub start_date: Option<String>,
    /// 结束日期
    pub end_date: Option<String>,
    /// 是否排他性
    pub is_exclusive: bool,
    /// 商业模式类型
    pub commercial_model_type: String,
}

/// 发行分组信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReleaseGrouping {
    /// 分组类型
    pub grouping_type: String,
    /// 序号
    pub sequence_number: i32,
    /// 资源引用列表
    pub resource_references: Vec<String>,
}
