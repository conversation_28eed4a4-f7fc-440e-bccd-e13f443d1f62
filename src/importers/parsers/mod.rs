//! 解析器接口和实现
//! 
//! 定义统一的解析器接口，每个音乐提供商实现自己的解析逻辑

use anyhow::Result;
use async_trait::async_trait;
use std::path::Path;

use crate::importers::models::ParsedRelease;

pub mod sony;
pub mod warner;
pub mod umg;

pub use sony::SonyParser;
pub use warner::WarnerParser;
pub use umg::UmgParser;

/// 解析器接口
/// 
/// 每个音乐提供商需要实现这个接口来处理自己的 XML 格式
#[async_trait]
pub trait Parser: Send + Sync {
    /// 获取提供商名称
    fn get_provider_name(&self) -> &str;
    
    /// 验证 XML 文件格式
    /// 
    /// 检查文件是否符合该提供商的格式要求
    fn validate_xml(&self, xml_path: &str) -> Result<()>;
    
    /// 解析 XML 文件
    /// 
    /// # 参数
    /// - `xml_path`: XML 文件路径
    /// - `base_dir`: 基础目录路径（用于解析相对路径的资源文件）
    /// 
    /// # 返回
    /// 解析后的标准化发行版本信息
    async fn parse_xml(&self, xml_path: &str, base_dir: &str) -> Result<ParsedRelease>;
    
    /// 获取支持的文件扩展名
    /// 
    /// 返回该提供商支持的音频和图片文件扩展名
    fn get_supported_extensions(&self) -> SupportedExtensions;
    
    /// 生成远程文件路径
    /// 
    /// 根据提供商的规则生成 NAS 上的文件路径
    fn generate_remote_path(&self, local_path: &str, date: &str) -> String;
    
    /// 预处理文件
    /// 
    /// 在解析前对文件进行预处理（可选）
    async fn preprocess_file(&self, _xml_path: &str) -> Result<()> {
        // 默认不做任何预处理
        Ok(())
    }
    
    /// 后处理解析结果
    /// 
    /// 在解析后对结果进行后处理（可选）
    async fn postprocess_result(&self, _result: &mut ParsedRelease) -> Result<()> {
        // 默认不做任何后处理
        Ok(())
    }
}

/// 支持的文件扩展名
#[derive(Debug, Clone)]
pub struct SupportedExtensions {
    /// 音频文件扩展名
    pub audio: Vec<String>,
    /// 图片文件扩展名
    pub image: Vec<String>,
}

impl SupportedExtensions {
    /// 创建默认的支持扩展名
    pub fn default() -> Self {
        Self {
            audio: vec![
                "mp3".to_string(),
                "flac".to_string(),
                "wav".to_string(),
                "aac".to_string(),
                "m4a".to_string(),
            ],
            image: vec![
                "jpg".to_string(),
                "jpeg".to_string(),
                "png".to_string(),
                "gif".to_string(),
                "bmp".to_string(),
            ],
        }
    }
    
    /// 检查是否为支持的音频文件
    pub fn is_audio_file(&self, path: &str) -> bool {
        if let Some(ext) = Path::new(path).extension().and_then(|e| e.to_str()) {
            self.audio.iter().any(|supported| supported.eq_ignore_ascii_case(ext))
        } else {
            false
        }
    }
    
    /// 检查是否为支持的图片文件
    pub fn is_image_file(&self, path: &str) -> bool {
        if let Some(ext) = Path::new(path).extension().and_then(|e| e.to_str()) {
            self.image.iter().any(|supported| supported.eq_ignore_ascii_case(ext))
        } else {
            false
        }
    }
    
    /// 检查是否为支持的文件
    pub fn is_supported_file(&self, path: &str) -> bool {
        self.is_audio_file(path) || self.is_image_file(path)
    }
}

/// 解析器工厂
pub struct ParserFactory;

impl ParserFactory {
    /// 创建解析器
    pub fn create_parser(provider: &str) -> Result<Box<dyn Parser>> {
        match provider.to_lowercase().as_str() {
            "sony" => Ok(Box::new(SonyParser::new())),
            "warner" => Ok(Box::new(WarnerParser::new())),
            "umg" => Ok(Box::new(UmgParser::new())),
            _ => Err(anyhow::anyhow!("Unsupported provider: {}", provider)),
        }
    }
    
    /// 获取所有支持的提供商
    pub fn get_supported_providers() -> Vec<&'static str> {
        vec!["sony", "warner", "umg"]
    }
    
    /// 检查是否支持该提供商
    pub fn is_supported_provider(provider: &str) -> bool {
        Self::get_supported_providers()
            .iter()
            .any(|&p| p.eq_ignore_ascii_case(provider))
    }
}

/// 解析器辅助函数
pub mod utils {
    use super::*;
    use std::fs;
    use walkdir::WalkDir;
    
    /// 查找目录中的 XML 文件
    pub fn find_xml_file(dir_path: &str) -> Result<String> {
        for entry in WalkDir::new(dir_path).max_depth(1) {
            let entry = entry?;
            if entry.file_type().is_file() {
                if let Some(extension) = entry.path().extension() {
                    if extension.eq_ignore_ascii_case("xml") {
                        return Ok(entry.path().to_string_lossy().to_string());
                    }
                }
            }
        }
        
        Err(anyhow::anyhow!("No XML file found in directory: {}", dir_path))
    }
    
    /// 扫描目录中的资源文件
    pub fn scan_resource_files(dir_path: &str, extensions: &SupportedExtensions) -> Result<(Vec<String>, Vec<String>)> {
        let mut audio_files = Vec::new();
        let mut image_files = Vec::new();
        
        for entry in WalkDir::new(dir_path) {
            let entry = entry?;
            if entry.file_type().is_file() {
                let path = entry.path().to_string_lossy().to_string();
                
                if extensions.is_audio_file(&path) {
                    audio_files.push(path);
                } else if extensions.is_image_file(&path) {
                    image_files.push(path);
                }
            }
        }
        
        Ok((audio_files, image_files))
    }
    
    /// 验证文件是否存在且可读
    pub fn validate_file_access(file_path: &str) -> Result<()> {
        let path = Path::new(file_path);
        
        if !path.exists() {
            return Err(anyhow::anyhow!("File does not exist: {}", file_path));
        }
        
        if !path.is_file() {
            return Err(anyhow::anyhow!("Path is not a file: {}", file_path));
        }
        
        // 尝试读取文件以验证权限
        fs::File::open(path)
            .map_err(|e| anyhow::anyhow!("Cannot read file {}: {}", file_path, e))?;
        
        Ok(())
    }
    
    /// 生成资源 ID
    pub fn generate_resource_id(file_path: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        file_path.hash(&mut hasher);
        format!("resource_{:x}", hasher.finish())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_supported_extensions() {
        let extensions = SupportedExtensions::default();
        
        assert!(extensions.is_audio_file("test.mp3"));
        assert!(extensions.is_audio_file("test.MP3"));
        assert!(extensions.is_audio_file("test.flac"));
        assert!(!extensions.is_audio_file("test.txt"));
        
        assert!(extensions.is_image_file("test.jpg"));
        assert!(extensions.is_image_file("test.PNG"));
        assert!(!extensions.is_image_file("test.mp3"));
        
        assert!(extensions.is_supported_file("test.mp3"));
        assert!(extensions.is_supported_file("test.jpg"));
        assert!(!extensions.is_supported_file("test.txt"));
    }
    
    #[test]
    fn test_parser_factory() {
        assert!(ParserFactory::is_supported_provider("sony"));
        assert!(ParserFactory::is_supported_provider("WARNER"));
        assert!(ParserFactory::is_supported_provider("umg"));
        assert!(!ParserFactory::is_supported_provider("unknown"));
        
        let providers = ParserFactory::get_supported_providers();
        assert_eq!(providers.len(), 3);
        assert!(providers.contains(&"sony"));
        assert!(providers.contains(&"warner"));
        assert!(providers.contains(&"umg"));
    }
    
    #[test]
    fn test_resource_id_generation() {
        let id1 = utils::generate_resource_id("/path/to/file1.mp3");
        let id2 = utils::generate_resource_id("/path/to/file2.mp3");
        let id3 = utils::generate_resource_id("/path/to/file1.mp3");
        
        assert_ne!(id1, id2);
        assert_eq!(id1, id3); // 相同路径应该生成相同ID
        assert!(id1.starts_with("resource_"));
    }
}
