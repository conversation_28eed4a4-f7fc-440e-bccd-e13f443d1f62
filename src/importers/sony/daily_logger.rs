//! Sony 每日日志记录器
//! 
//! 负责记录每个文件的处理结果，采用每日记录格式

use super::SonyError;
use anyhow::Result;
use chrono::{DateTime, Local, Utc};
use log::{debug, info};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs::{self, OpenOptions};
use std::io::Write;
use std::path::PathBuf;

/// 处理结果类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ProcessResult {
    /// 解析成功
    ParseSuccess,
    /// 解析失败
    ParseFailed,
    /// 上传成功
    UploadSuccess,
    /// 上传失败
    UploadFailed,
    /// 数据库保存成功
    DatabaseSuccess,
    /// 数据库保存失败
    DatabaseFailed,
    /// 跳过处理
    Skipped,
}

/// 单个文件的处理记录
#[derive(Debug, C<PERSON>, Serialize, Deserialize)]
pub struct ProcessRecord {
    /// 文件路径
    pub file_path: String,
    /// 处理开始时间
    pub start_time: DateTime<Utc>,
    /// 处理结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// 处理结果
    pub result: ProcessResult,
    /// 专辑标题
    pub album_title: Option<String>,
    /// GRid 标识符
    pub grid: Option<String>,
    /// 曲目数量
    pub track_count: Option<usize>,
    /// 音频资源数量
    pub audio_resource_count: Option<usize>,
    /// 图片资源数量
    pub image_resource_count: Option<usize>,
    /// NAS 文件路径列表
    pub nas_file_paths: Vec<String>,
    /// 错误信息
    pub error_message: Option<String>,
    /// 处理耗时（毫秒）
    pub duration_ms: Option<i64>,
}

/// 每日日志记录器
pub struct DailyLogger {
    /// 日志目录
    log_dir: PathBuf,
    /// 当前日期
    current_date: String,
    /// 当前日志文件路径
    current_log_file: PathBuf,
    /// 内存中的记录缓存
    records_cache: HashMap<String, ProcessRecord>,
}

impl DailyLogger {
    /// 创建新的每日日志记录器
    pub fn new(log_dir: &str) -> Result<Self, SonyError> {
        let log_dir = PathBuf::from(log_dir);
        
        // 确保日志目录存在
        fs::create_dir_all(&log_dir)
            .map_err(|e| SonyError::IoError(e))?;
        
        let current_date = Local::now().format("%Y-%m-%d").to_string();
        let current_log_file = log_dir.join(format!("sony_process_{}.jsonl", current_date));
        
        info!("初始化每日日志记录器: {}", current_log_file.display());
        
        Ok(Self {
            log_dir,
            current_date,
            current_log_file,
            records_cache: HashMap::new(),
        })
    }
    
    /// 开始记录文件处理
    pub fn start_processing(&mut self, file_path: &str) -> Result<(), SonyError> {
        let record = ProcessRecord {
            file_path: file_path.to_string(),
            start_time: Utc::now(),
            end_time: None,
            result: ProcessResult::ParseSuccess, // 临时状态
            album_title: None,
            grid: None,
            track_count: None,
            audio_resource_count: None,
            image_resource_count: None,
            nas_file_paths: Vec::new(),
            error_message: None,
            duration_ms: None,
        };
        
        self.records_cache.insert(file_path.to_string(), record);
        debug!("开始记录文件处理: {}", file_path);
        
        Ok(())
    }
    
    /// 记录解析成功
    pub fn record_parse_success(
        &mut self,
        file_path: &str,
        album_title: &str,
        grid: &str,
        track_count: usize,
        audio_resource_count: usize,
        image_resource_count: usize,
    ) -> Result<(), SonyError> {
        if let Some(record) = self.records_cache.get_mut(file_path) {
            record.result = ProcessResult::ParseSuccess;
            record.album_title = Some(album_title.to_string());
            record.grid = Some(grid.to_string());
            record.track_count = Some(track_count);
            record.audio_resource_count = Some(audio_resource_count);
            record.image_resource_count = Some(image_resource_count);
            
            info!("记录解析成功: {} - {}", file_path, album_title);
        }
        
        Ok(())
    }
    
    /// 记录解析失败
    pub fn record_parse_failed(&mut self, file_path: &str, error: &str) -> Result<(), SonyError> {
        if let Some(record) = self.records_cache.get_mut(file_path) {
            record.result = ProcessResult::ParseFailed;
            record.error_message = Some(error.to_string());
            
            info!("记录解析失败: {} - {}", file_path, error);
        }
        
        Ok(())
    }
    
    /// 记录上传成功
    pub fn record_upload_success(&mut self, file_path: &str, nas_file_paths: &[String]) -> Result<(), SonyError> {
        if let Some(record) = self.records_cache.get_mut(file_path) {
            record.result = ProcessResult::UploadSuccess;
            record.nas_file_paths = nas_file_paths.to_vec();
            
            info!("记录上传成功: {} - {} 个文件", file_path, nas_file_paths.len());
        }
        
        Ok(())
    }
    
    /// 记录数据库保存成功
    pub fn record_database_success(&mut self, file_path: &str) -> Result<(), SonyError> {
        if let Some(record) = self.records_cache.get_mut(file_path) {
            record.result = ProcessResult::DatabaseSuccess;
            
            info!("记录数据库保存成功: {}", file_path);
        }
        
        Ok(())
    }
    
    /// 完成文件处理并写入日志
    pub fn finish_processing(&mut self, file_path: &str) -> Result<(), SonyError> {
        if let Some(mut record) = self.records_cache.remove(file_path) {
            let end_time = Utc::now();
            record.end_time = Some(end_time);
            
            // 计算处理耗时
            record.duration_ms = Some(
                (end_time - record.start_time).num_milliseconds()
            );
            
            // 写入日志文件
            self.write_record_to_file(&record)?;
            
            info!("完成文件处理记录: {} (耗时: {}ms)", 
                  file_path, 
                  record.duration_ms.unwrap_or(0));
        }
        
        Ok(())
    }
    
    /// 记录跳过的文件
    pub fn record_skipped(&mut self, file_path: &str, reason: &str) -> Result<(), SonyError> {
        let record = ProcessRecord {
            file_path: file_path.to_string(),
            start_time: Utc::now(),
            end_time: Some(Utc::now()),
            result: ProcessResult::Skipped,
            album_title: None,
            grid: None,
            track_count: None,
            audio_resource_count: None,
            image_resource_count: None,
            nas_file_paths: Vec::new(),
            error_message: Some(reason.to_string()),
            duration_ms: Some(0),
        };
        
        self.write_record_to_file(&record)?;
        info!("记录跳过文件: {} - {}", file_path, reason);
        
        Ok(())
    }
    
    /// 写入记录到文件
    fn write_record_to_file(&self, record: &ProcessRecord) -> Result<(), SonyError> {
        // 检查是否需要切换日志文件（日期变化）
        let current_date = Local::now().format("%Y-%m-%d").to_string();
        let log_file = if current_date != self.current_date {
            self.log_dir.join(format!("sony_process_{}.jsonl", current_date))
        } else {
            self.current_log_file.clone()
        };
        
        // 序列化记录为 JSON
        let json_line = serde_json::to_string(record)
            .map_err(|e| SonyError::ConversionError(format!("序列化记录失败: {}", e)))?;
        
        // 追加写入文件
        let mut file = OpenOptions::new()
            .create(true)
            .append(true)
            .open(&log_file)
            .map_err(|e| SonyError::IoError(e))?;
        
        writeln!(file, "{}", json_line)
            .map_err(|e| SonyError::IoError(e))?;
        
        debug!("写入日志记录到: {}", log_file.display());
        
        Ok(())
    }
    
    /// 获取今日统计信息
    pub fn get_daily_statistics(&self) -> Result<DailyStatistics, SonyError> {
        let log_file = &self.current_log_file;
        
        if !log_file.exists() {
            return Ok(DailyStatistics::default());
        }
        
        let content = fs::read_to_string(log_file)
            .map_err(|e| SonyError::IoError(e))?;
        
        let mut stats = DailyStatistics::default();
        
        for line in content.lines() {
            if line.trim().is_empty() {
                continue;
            }
            
            let record: ProcessRecord = serde_json::from_str(line)
                .map_err(|e| SonyError::ConversionError(format!("解析日志记录失败: {}", e)))?;
            
            match record.result {
                ProcessResult::ParseSuccess => stats.parse_success += 1,
                ProcessResult::ParseFailed => stats.parse_failed += 1,
                ProcessResult::UploadSuccess => stats.upload_success += 1,
                ProcessResult::UploadFailed => stats.upload_failed += 1,
                ProcessResult::DatabaseSuccess => stats.database_success += 1,
                ProcessResult::DatabaseFailed => stats.database_failed += 1,
                ProcessResult::Skipped => stats.skipped += 1,
            }
            
            stats.total_files += 1;
            stats.total_duration_ms += record.duration_ms.unwrap_or(0);
        }
        
        Ok(stats)
    }
}

/// 每日统计信息
#[derive(Debug, Default)]
pub struct DailyStatistics {
    pub total_files: usize,
    pub parse_success: usize,
    pub parse_failed: usize,
    pub upload_success: usize,
    pub upload_failed: usize,
    pub database_success: usize,
    pub database_failed: usize,
    pub skipped: usize,
    pub total_duration_ms: i64,
}

impl DailyStatistics {
    /// 获取平均处理时间（毫秒）
    pub fn average_duration_ms(&self) -> f64 {
        if self.total_files > 0 {
            self.total_duration_ms as f64 / self.total_files as f64
        } else {
            0.0
        }
    }
    
    /// 获取成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_files > 0 {
            (self.database_success as f64 / self.total_files as f64) * 100.0
        } else {
            0.0
        }
    }
}
