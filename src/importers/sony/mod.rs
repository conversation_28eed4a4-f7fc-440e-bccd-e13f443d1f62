//! Sony Music 导入器模块
//!
//! 这个模块提供了完整的 Sony Music DDEX ERN XML 解析和文件处理功能。
//!
//! # 模块结构
//! - `ddex_parser`: DDEX ERN XML 解析器
//! - `utils`: 工具函数和数据转换
//! - `importer`: 主要导入器实现
//! - `parse_tracker`: 解析状态跟踪器
//! - `daily_logger`: 每日日志记录器

use serde::{Deserialize, Serialize};

// 导出子模块
pub mod ddex_parser;
pub mod utils;
pub mod importer;
pub mod daily_logger;
pub mod simple_status_manager;
pub mod simple_parse_tracker;

// #[cfg(test)]
// pub mod test_parser;

// 重新导出主要类型
pub use ddex_parser::DdexParser;
pub use importer::SonyImporter;
pub use simple_parse_tracker::{SimpleParseTracker, ParseSummary, ParseStatus};
pub use daily_logger::{DailyLogger, ProcessResult, ProcessRecord, DailyStatistics};
pub use utils::*;

/// Sony 模块的错误类型
#[derive(Debug, thiserror::Error)]
pub enum SonyError {
    #[error("XML 解析错误: {0}")]
    XmlParseError(String),
    
    #[error("文件下载错误: {0}")]
    DownloadError(String),
    
    #[error("数据转换错误: {0}")]
    ConversionError(String),
    
    #[error("文件验证错误: {0}")]
    ValidationError(String),
    
    #[error("集成错误: {0}")]
    IntegrationError(String),
    
    #[error("IO 错误: {0}")]
    IoError(#[from] std::io::Error),
    
    #[error("网络错误: {0}")]
    NetworkError(#[from] reqwest::Error),
    
    #[error("其他错误: {0}")]
    Other(#[from] anyhow::Error),
}

/// 解析后的发行版本信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParsedRelease {
    /// 消息头信息
    pub message_header: MessageHeader,
    /// 专辑信息
    pub album_info: AlbumInfo,
    /// 曲目列表
    pub tracks: Vec<TrackInfo>,
    /// 音频资源
    pub audio_resources: Vec<AudioResource>,
    /// 图片资源
    pub image_resources: Vec<ImageResource>,
}

/// 消息头信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageHeader {
    /// 消息线程 ID
    pub message_thread_id: String,
    /// 消息 ID
    pub message_id: String,
    /// 发送方信息
    pub sender: PartyInfo,
    /// 接收方信息
    pub recipient: PartyInfo,
    /// 创建时间
    pub created_datetime: String,
    /// 控制类型
    pub control_type: String,
}

/// 参与方信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PartyInfo {
    /// 参与方 ID
    pub party_id: String,
    /// 参与方名称
    pub party_name: String,
}

/// 专辑信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlbumInfo {
    /// GRid 标识符
    pub grid: String,
    /// ICPN 标识符
    pub icpn: Option<String>,
    /// 目录号
    pub catalog_number: Option<String>,
    /// 标题
    pub title: String,
    /// 正式标题
    pub formal_title: Option<String>,
    /// 显示艺术家
    pub display_artist_name: String,
    /// 艺术家列表
    pub artists: Vec<ArtistInfo>,
    /// 厂牌名称
    pub label_name: String,
    /// 发行类型
    pub release_type: String,
    /// 发行日期
    pub release_date: Option<String>,
    /// 总时长（秒）
    pub duration: i64,
    /// 版权信息
    pub copyright: String,
    /// 流派
    pub genre: Option<String>,
    /// 分级警告
    pub parental_warning: Option<String>,
}

/// 曲目信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrackInfo {
    /// 资源引用
    pub resource_reference: String,
    /// ISRC 标识符
    pub isrc: String,
    /// 标题
    pub title: String,
    /// 正式标题
    pub formal_title: Option<String>,
    /// 时长（秒）
    pub duration: i64,
    /// 曲目序号
    pub sequence_number: i32,
    /// 艺术家列表
    pub artists: Vec<ArtistInfo>,
    /// 贡献者列表
    pub contributors: Vec<ContributorInfo>,
    /// 语言
    pub language: Option<String>,
    /// 流派
    pub genre: Option<String>,
}

/// 艺术家信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ArtistInfo {
    /// 艺术家名称
    pub name: String,
    /// 角色列表
    pub roles: Vec<String>,
    /// 序号
    pub sequence_number: Option<i32>,
    /// 语言代码
    pub language_code: Option<String>,
}

/// 贡献者信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ContributorInfo {
    /// 贡献者名称
    pub name: String,
    /// 角色
    pub role: String,
    /// 序号
    pub sequence_number: Option<i32>,
    /// 命名空间
    pub namespace: Option<String>,
    /// 用户定义值
    pub user_defined_value: Option<String>,
}

/// 音频资源
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AudioResource {
    /// 资源引用
    pub resource_reference: String,
    /// 下载 URL
    pub url: String,
    /// 本地文件路径
    pub local_file_path: String,
    /// MD5 哈希
    pub md5: String,
    /// 音频编码类型
    pub codec_type: String,
    /// 比特率
    pub bit_rate: Option<u32>,
    /// 声道数
    pub channels: Option<u32>,
    /// 采样率
    pub sampling_rate: Option<f32>,
    /// 位深度
    pub bits_per_sample: Option<u32>,
    /// 是否为预览
    pub is_preview: bool,
    /// 预览详情
    pub preview_details: Option<PreviewDetails>,
}

/// 图片资源
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ImageResource {
    /// 资源引用
    pub resource_reference: String,
    /// 图片类型
    pub image_type: String,
    /// 下载 URL
    pub url: String,
    /// 本地文件路径
    pub local_file_path: String,
    /// MD5 哈希
    pub md5: String,
    /// 图片编码类型
    pub codec_type: String,
    /// 图片高度
    pub height: Option<u32>,
    /// 图片宽度
    pub width: Option<u32>,
    /// 分辨率
    pub resolution: Option<u32>,
    /// 是否为预览
    pub is_preview: bool,
}

/// 预览详情
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PreviewDetails {
    /// 开始点（秒）
    pub start_point: Option<u32>,
    /// 结束点（秒）
    pub end_point: Option<u32>,
    /// 时长（秒）
    pub duration: Option<u32>,
    /// 表达类型
    pub expression_type: Option<String>,
}

/// 下载的文件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DownloadedFile {
    /// 资源引用
    pub resource_reference: String,
    /// 本地文件路径
    pub local_path: String,
    /// 原始 URL
    pub original_url: String,
    /// 文件大小
    pub file_size: u64,
    /// MD5 哈希
    pub md5: String,
    /// 文件类型
    pub file_type: FileType,
}

/// 文件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FileType {
    Audio,
    Image,
}

/// 处理结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingResult {
    /// 是否成功
    pub success: bool,
    /// 处理的文件数量
    pub processed_files: usize,
    /// 失败的文件数量
    pub failed_files: usize,
    /// 错误信息
    pub errors: Vec<String>,
    /// 下载的文件列表
    pub downloaded_files: Vec<DownloadedFile>,
}

impl Default for ProcessingResult {
    fn default() -> Self {
        Self {
            success: true,
            processed_files: 0,
            failed_files: 0,
            errors: Vec::new(),
            downloaded_files: Vec::new(),
        }
    }
}
