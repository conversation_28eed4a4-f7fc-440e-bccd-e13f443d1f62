//! 数据更新模块
//! 
//! 负责根据数据对比结果执行相应的数据库更新操作

use anyhow::{Result, Context};
use log::{info, debug};

use crate::utils::db::Database;
use crate::importers::models::{AlbumInfo, TrackInfo};
use super::data_comparator::{AlbumComparison, TrackComparison, ComparisonResult};

/// 更新操作统计
#[derive(Debug, Default)]
pub struct UpdateStats {
    pub albums_inserted: u32,
    pub albums_updated: u32,
    pub albums_unchanged: u32,
    pub tracks_inserted: u32,
    pub tracks_updated: u32,
    pub tracks_unchanged: u32,
}

/// 数据更新器
pub struct DataUpdater {
    /// 是否启用详细更新日志
    pub verbose_logging: bool,
}

impl DataUpdater {
    /// 创建新的数据更新器
    pub fn new(verbose_logging: bool) -> Self {
        Self { verbose_logging }
    }

    /// 根据对比结果处理专辑数据
    pub async fn handle_album(&self, 
                              db: &Database, 
                              album_info: &AlbumInfo, 
                              comparison: &AlbumComparison,
                              stats: &mut UpdateStats) -> Result<i64> {
        match &comparison.result {
            ComparisonResult::New => {
                // 插入新专辑
                let album_id = self.insert_album(db, album_info).await?;
                stats.albums_inserted += 1;
                info!("✅ 新增专辑: {} (ID: {}, GRid: {})", 
                      album_info.title, album_id, 
                      album_info.grid.as_ref().unwrap_or(&"unknown".to_string()));
                Ok(album_id)
            }
            ComparisonResult::Unchanged => {
                // 数据无变化，返回现有ID
                let album_id = comparison.album_id.unwrap();
                stats.albums_unchanged += 1;
                if self.verbose_logging {
                    debug!("⏭️  专辑数据无变化: {} (ID: {})", album_info.title, album_id);
                }
                Ok(album_id)
            }
            ComparisonResult::Changed(changes) => {
                // 更新现有专辑
                let album_id = comparison.album_id.unwrap();
                self.update_album(db, album_info, album_id, changes).await?;
                stats.albums_updated += 1;
                info!("🔄 更新专辑: {} (ID: {}), 变化: {:?}", 
                      album_info.title, album_id, changes);
                Ok(album_id)
            }
        }
    }

    /// 根据对比结果处理曲目数据
    pub async fn handle_track(&self, 
                              db: &Database, 
                              track_info: &TrackInfo, 
                              comparison: &TrackComparison,
                              nas_file_path: &str,
                              stats: &mut UpdateStats) -> Result<i64> {
        match &comparison.result {
            ComparisonResult::New => {
                // 插入新曲目
                let track_id = self.insert_track(db, track_info, nas_file_path).await?;
                stats.tracks_inserted += 1;
                info!("✅ 新增曲目: {} (ID: {}, ISRC: {:?})", 
                      track_info.title, track_id, track_info.isrc);
                Ok(track_id)
            }
            ComparisonResult::Unchanged => {
                // 数据无变化，返回现有ID
                let track_id = comparison.track_id.unwrap();
                stats.tracks_unchanged += 1;
                if self.verbose_logging {
                    debug!("⏭️  曲目数据无变化: {} (ID: {})", track_info.title, track_id);
                }
                Ok(track_id)
            }
            ComparisonResult::Changed(changes) => {
                // 更新现有曲目
                let track_id = comparison.track_id.unwrap();
                self.update_track(db, track_info, track_id, changes, nas_file_path).await?;
                stats.tracks_updated += 1;
                info!("🔄 更新曲目: {} (ID: {}), 变化: {:?}", 
                      track_info.title, track_id, changes);
                Ok(track_id)
            }
        }
    }

    /// 插入新专辑
    async fn insert_album(&self, db: &Database, album_info: &AlbumInfo) -> Result<i64> {
        let query_insert = r#"
            INSERT INTO album (
                album_name, grid, icpn, releasedate, duration, 
                cover_link, active, created_date, isCustom, domain
            ) VALUES (?, ?, ?, ?, 
                CASE 
                    WHEN ? > 0 THEN SEC_TO_TIME(?)
                    ELSE NULL 
                END, 
                ?, 1, NOW(), 0, 1)
        "#;

        let grid = album_info.grid.as_ref().unwrap_or(&"unknown".to_string()).clone();
        let duration_seconds = 0i32; // 暂时设为0，后续可以从曲目计算
        let params = (
            &album_info.title,           // album_name
            &grid,                       // grid
            album_info.upc.as_ref(),     // icpn (使用UPC作为ICPN)
            &album_info.release_date,    // releasedate
            &duration_seconds,           // duration 判断条件
            &duration_seconds,           // duration 转换值
            None::<String>,              // cover_link (暂时为空)
        );

        let album_id = db.execute_insert(query_insert, params).await
            .context("Failed to insert new album")?;

        Ok(album_id)
    }

    /// 更新现有专辑
    async fn update_album(&self, 
                          db: &Database, 
                          album_info: &AlbumInfo, 
                          album_id: i64,
                          _changes: &[String]) -> Result<()> {
        let query_update = r#"
            UPDATE album SET 
                album_name = ?,
                icpn = ?,
                releasedate = ?,
                updated_date = NOW()
            WHERE album_id = ?
        "#;

        let params = (
            &album_info.title,           // album_name
            album_info.upc.as_ref(),     // icpn
            &album_info.release_date,    // releasedate
            album_id,                    // WHERE条件
        );

        db.execute(query_update, params).await
            .context("Failed to update album")?;

        Ok(())
    }

    /// 插入新曲目
    async fn insert_track(&self, db: &Database, track_info: &TrackInfo, nas_file_path: &str) -> Result<i64> {
        let query_insert = r#"
            INSERT INTO track (
                isrc, trackname, duration, filelocation, md5,
                discno, trackno, releasetype, active, created_date,
                isCustom, domain
            ) VALUES (?, ?, 
                CASE 
                    WHEN ? > 0 THEN SEC_TO_TIME(?)
                    ELSE NULL 
                END, 
                ?, ?, 1, ?, 'Single', 1, NOW(), 0, 1)
        "#;

        let duration_seconds = track_info.duration_seconds as i32;
        let params = (
            track_info.isrc.as_ref(),                                                    // isrc
            &track_info.title,                                                          // trackname
            &duration_seconds,                                                          // duration 判断条件
            &duration_seconds,                                                          // duration 转换值
            if nas_file_path.is_empty() { None } else { Some(nas_file_path) },         // filelocation
            None::<String>,                                                             // md5 (暂时为空)
            track_info.track_number as i32,                                            // trackno
        );

        let track_id = db.execute_insert(query_insert, params).await
            .context("Failed to insert new track")?;

        Ok(track_id)
    }

    /// 更新现有曲目
    async fn update_track(&self, 
                          db: &Database, 
                          track_info: &TrackInfo, 
                          track_id: i64,
                          _changes: &[String],
                          nas_file_path: &str) -> Result<()> {
        let query_update = r#"
            UPDATE track SET 
                trackname = ?,
                duration = CASE 
                    WHEN ? > 0 THEN SEC_TO_TIME(?)
                    ELSE duration 
                END,
                filelocation = COALESCE(?, filelocation),
                trackno = ?,
                updated_date = NOW()
            WHERE track_id = ?
        "#;

        let duration_seconds = track_info.duration_seconds as i32;
        let params = (
            &track_info.title,                                                          // trackname
            &duration_seconds,                                                          // duration 判断条件
            &duration_seconds,                                                          // duration 转换值
            if nas_file_path.is_empty() { None } else { Some(nas_file_path) },         // filelocation
            track_info.track_number as i32,                                            // trackno
            track_id,                                                                   // WHERE条件
        );

        db.execute(query_update, params).await
            .context("Failed to update track")?;

        Ok(())
    }

    /// 检查专辑-曲目关联是否存在
    pub async fn check_album_track_link(&self, db: &Database, album_id: i64, track_id: i64) -> Result<bool> {
        let query = "SELECT id FROM m3_album_link WHERE album_id = ? AND track_id = ?";
        
        match db.query(query, (album_id, track_id)).await {
            Ok(rows) => Ok(!rows.is_empty()),
            Err(_) => Ok(false), // 查询失败时假设不存在
        }
    }

    /// 插入专辑-曲目关联（如果不存在）
    pub async fn ensure_album_track_link(&self, db: &Database, album_id: i64, track_id: i64) -> Result<()> {
        if !self.check_album_track_link(db, album_id, track_id).await? {
            let query = r#"
                INSERT INTO m3_album_link (track_id, album_id, created_date, created_by)
                VALUES (?, ?, NOW(), 1000010001)
            "#;

            db.execute(query, (track_id, album_id)).await
                .context("Failed to insert album-track link")?;

            debug!("创建专辑-曲目关联: album_id={}, track_id={}", album_id, track_id);
        }
        Ok(())
    }

    /// 检查曲目-艺术家关联是否存在
    pub async fn check_track_artist_link(&self, db: &Database, track_id: i64, artist_id: i64, role_id: i32) -> Result<bool> {
        let query = "SELECT id FROM m3_artist_link WHERE track_id = ? AND artist_id = ? AND role_id = ?";
        
        match db.query(query, (track_id, artist_id, role_id)).await {
            Ok(rows) => Ok(!rows.is_empty()),
            Err(_) => Ok(false), // 查询失败时假设不存在
        }
    }

    /// 插入曲目-艺术家关联（如果不存在）
    pub async fn ensure_track_artist_link(&self, db: &Database, track_id: i64, artist_id: i64, role_id: i32) -> Result<()> {
        if !self.check_track_artist_link(db, track_id, artist_id, role_id).await? {
            let query = r#"
                INSERT INTO m3_artist_link (track_id, artist_id, role_id, created_date, created_by)
                VALUES (?, ?, ?, NOW(), 1000010001)
            "#;

            db.execute(query, (track_id, artist_id, role_id)).await
                .context("Failed to insert track-artist link")?;

            debug!("创建曲目-艺术家关联: track_id={}, artist_id={}, role_id={}", track_id, artist_id, role_id);
        }
        Ok(())
    }

    /// 打印更新统计信息
    pub fn print_stats(&self, stats: &UpdateStats) {
        info!("📊 数据处理统计:");
        info!("  专辑: 新增 {}, 更新 {}, 无变化 {}", 
              stats.albums_inserted, stats.albums_updated, stats.albums_unchanged);
        info!("  曲目: 新增 {}, 更新 {}, 无变化 {}", 
              stats.tracks_inserted, stats.tracks_updated, stats.tracks_unchanged);
    }
}
