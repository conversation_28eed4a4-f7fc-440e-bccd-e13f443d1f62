//! 通用导入引擎
//! 
//! 提供统一的导入流程管理，处理文件扫描、解析、上传、状态跟踪等通用功能

use anyhow::Result;
use log::{info, error, warn, debug};
use std::collections::HashMap;

use crate::config::Config;
use crate::importers::models::*;
use crate::importers::parsers::{Parser, ParserFactory, utils as parser_utils};
use crate::utils::grpc_client::GrpcFileClient;
use crate::utils::db::Database;

pub mod file_processor;
pub mod status_tracker;
pub mod logger;
pub mod data_comparator;
pub mod data_updater;

pub use file_processor::FileProcessor;
pub use status_tracker::StatusTracker;
pub use logger::ImportLogger;
pub use data_comparator::DataComparator;
pub use data_updater::{DataUpdater, UpdateStats};

/// 导入引擎
/// 
/// 统一管理所有提供商的导入流程，提供通用的功能实现
pub struct ImportEngine {
    /// 配置信息
    config: Config,
    /// 日期标识
    date: String,
    /// 解析器
    parser: Box<dyn Parser>,
    /// 文件处理器
    file_processor: FileProcessor,
    /// 状态跟踪器
    status_tracker: Option<StatusTracker>,
    /// 日志记录器
    logger: Option<ImportLogger>,
    /// gRPC 客户端
    grpc_client: Option<GrpcFileClient>,
    /// 数据库连接
    database: Option<Database>,
    /// 数据对比器
    data_comparator: DataComparator,
    /// 数据更新器
    data_updater: DataUpdater,
}

/// 导入结果
#[derive(Debug)]
pub enum ImportResult {
    Success,
    Failure(String),
}

/// 详细导入结果（用于导入器返回）
#[derive(Debug, Clone)]
pub struct DetailedImportResult {
    /// 是否成功
    pub success: bool,
    /// 专辑标题
    pub album_title: String,
    /// GRid 标识符
    pub grid: String,
    /// 曲目数量
    pub track_count: usize,
    /// 音频资源数量
    pub audio_resource_count: usize,
    /// 图片资源数量
    pub image_resource_count: usize,
    /// 消息
    pub message: String,
    /// 错误列表
    pub errors: Vec<String>,
}

/// 处理结果
#[derive(Debug)]
pub enum ProcessResult {
    Success,
    Skipped,
}

impl ImportEngine {
    /// 获取配置信息
    pub fn get_config(&self) -> &Config {
        &self.config
    }

    /// 创建新的导入引擎
    pub fn new(config: Config, date: String, provider: &str) -> Result<Self> {
        info!("Creating import engine for provider: {}", provider);
        
        // 创建解析器
        let parser = ParserFactory::create_parser(provider)?;
        
        // 创建文件处理器
        let file_processor = FileProcessor::new(config.clone());

        Ok(Self {
            config,
            date,
            parser,
            file_processor,
            status_tracker: None,
            logger: None,
            grpc_client: None,
            database: None,
            data_comparator: DataComparator::new(true), // 启用详细日志
            data_updater: DataUpdater::new(true),       // 启用详细日志
        })
    }

    /// 生成基于专辑ID的稳定时间戳
    ///
    /// 使用专辑ID的哈希值生成一个看起来像时间戳的稳定字符串
    /// 这样相同的专辑ID总是生成相同的"时间戳"
    fn generate_stable_timestamp_static(album_id: &str) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        album_id.hash(&mut hasher);
        let hash = hasher.finish();

        // 将哈希值转换为看起来像时间戳的格式
        // 格式：YYYYMMDDHHMMSSXXX
        let year = 2022 + (hash % 3) as u32; // 2022-2024
        let month = 1 + (hash >> 8) % 12; // 1-12
        let day = 1 + (hash >> 16) % 28; // 1-28 (避免月份天数问题)
        let hour = (hash >> 24) % 24; // 0-23
        let minute = (hash >> 32) % 60; // 0-59
        let second = (hash >> 40) % 60; // 0-59
        let millis = (hash >> 48) % 1000; // 0-999

        format!("{:04}{:02}{:02}{:02}{:02}{:02}{:03}",
                year, month, day, hour, minute, second, millis)
    }

    /// 初始化导入引擎（所有组件必须成功）
    pub async fn initialize(&mut self) -> Result<()> {
        info!("🔧 初始化导入引擎...");

        // 初始化状态跟踪器（必须成功）
        let status_dir = format!("{}/status", self.config.log_folder);
        self.status_tracker = Some(StatusTracker::new(&status_dir, &self.config.root_folder).await
            .map_err(|e| anyhow::anyhow!("Status tracker initialization failed: {}", e))?);
        info!("✅ 状态跟踪器初始化完成");

        // 初始化日志记录器（必须成功）
        let log_dir = format!("{}/daily", self.config.log_folder);
        self.logger = Some(ImportLogger::new(&log_dir)
            .map_err(|e| anyhow::anyhow!("Logger initialization failed: {}", e))?);
        info!("✅ 日志记录器初始化完成");

        // 初始化 gRPC 客户端（必须成功）
        info!("🔗 检查 gRPC 服务器连通性...");
        if let Some(ref grpc_config) = self.config.grpc {
            let client = GrpcFileClient::new(&grpc_config.server_addr).await
                .map_err(|e| anyhow::anyhow!("gRPC client initialization failed: {}", e))?;
            self.grpc_client = Some(client);
            info!("✅ gRPC 服务器连接成功");
        } else {
            return Err(anyhow::anyhow!("gRPC configuration is required"));
        }

        // 初始化数据库连接（必须成功）
        info!("🗄️ 初始化 MySQL 数据库连接...");
        let database = Database::new(&self.config.database)
            .map_err(|e| anyhow::anyhow!("Database initialization failed: {}", e))?;
        self.database = Some(database);
        info!("✅ MySQL 数据库连接成功");

        info!("🎉 导入引擎初始化完成");
        Ok(())
    }

    /// 开始导入流程（严格错误处理）
    pub async fn start_import(&mut self) -> ImportResult {
        info!("🚀 开始 {} 导入流程", self.parser.get_provider_name());

        // 初始化导入器（任何组件失败都会导致整个导入失败）
        if let Err(e) = self.initialize().await {
            error!("Error: Failed to initialize importer: {}", e);
            return ImportResult::Failure(e.to_string());
        }

        // 获取要处理的目录列表
        let directories = match self.file_processor.get_directories() {
            Ok(dirs) => dirs,
            Err(e) => {
                error!("获取目录列表失败: {}", e);
                return ImportResult::Failure(e.to_string());
            }
        };

        info!("📁 找到 {} 个目录待处理", directories.len());

        // 并行处理目录
        let processed_dirs = match self.file_processor.process_directories_parallel(directories, self.config.thread_count) {
            Ok(dirs) => dirs,
            Err(e) => {
                error!("并行处理目录失败: {}", e);
                return ImportResult::Failure(e.to_string());
            }
        };

        let mut processed = 0;
        let mut skipped = 0;
        let mut failed = 0;

        // 串行处理每个目录
        for (index, dir) in processed_dirs.iter().enumerate() {
            info!("📂 处理目录 {}/{}: {}", index + 1, processed_dirs.len(), dir);

            match self.process_single_directory(dir).await {
                Ok(result) => {
                    match result {
                        ProcessResult::Success => {
                            processed += 1;
                            info!("✅ 目录 {} 处理完成", dir);
                        }
                        ProcessResult::Skipped => {
                            skipped += 1;
                            info!("⏭️  目录 {} 已跳过", dir);
                        }
                    }
                }
                Err(e) => {
                    failed += 1;
                    error!("❌ 处理目录 {} 失败: {}", dir, e);
                }
            }
        }

        // 显示统计信息
        info!("📊 处理完成统计:");
        info!("   ✅ 成功处理: {} 个目录", processed);
        info!("   ⏭️  跳过目录: {} 个目录", skipped);
        info!("   ❌ 失败目录: {} 个目录", failed);

        info!("✅ {} 导入流程完成", self.parser.get_provider_name());
        ImportResult::Success
    }

    /// 处理单个目录
    async fn process_single_directory(&mut self, dir: &str) -> Result<ProcessResult> {
        info!("📂 处理目录: {}", dir);
        
        let full_path = format!("{}/{}", self.config.root_folder, dir);
        
        // 查找 XML 文件
        let xml_path = match parser_utils::find_xml_file(&full_path) {
            Ok(path) => path,
            Err(e) => {
                warn!("目录 {} 中未找到 XML 文件: {}", dir, e);
                return Ok(ProcessResult::Skipped);
            }
        };
        
        info!("📄 找到 XML 文件: {}", xml_path);
        
        // 检查解析状态（除非强制更新）
        if let Some(ref status_tracker) = self.status_tracker {
            if !self.config.force_update && status_tracker.is_successfully_parsed(&xml_path).await? {
                info!("⏭️  文件已成功解析，跳过: {}", xml_path);
                info!("💡 提示：使用 force_update=true 可以重新处理此文件");
                return Ok(ProcessResult::Skipped);
            } else if self.config.force_update {
                info!("🔄 强制更新模式：重新处理文件 {}", xml_path);
            }
        }
        
        // 标记开始解析
        if let Some(ref mut status_tracker) = self.status_tracker {
            status_tracker.mark_parsing_start(&xml_path).await?;
        }
        
        // 记录开始处理
        if let Some(ref mut logger) = self.logger {
            logger.start_processing(&xml_path)?;
        }
        
        // 第一步：解析 DDEX ERN XML 内容
        info!("🎵 步骤1: 解析 DDEX ERN XML 内容");
        let parsed_release = match self.parse_xml_file(&xml_path, &full_path).await {
            Ok(release) => {
                info!("✅ XML 解析成功: {}", xml_path);

                // 标记解析成功
                if let Some(ref mut status_tracker) = self.status_tracker {
                    status_tracker.mark_parsing_success(&xml_path).await?;
                }

                // 记录解析成功
                if let Some(ref mut logger) = self.logger {
                    logger.record_stage(&xml_path, "XML解析", true, Some(format!(
                        "专辑: {}, 曲目: {}, 音频: {}, 图片: {}",
                        release.album_info.title,
                        release.tracks.len(),
                        release.audio_resources.len(),
                        release.image_resources.len()
                    )))?;

                    // 记录每日日志
                    logger.record_daily_parse_success(
                        &xml_path,
                        &release.album_info.title,
                        &release.album_info.grid.as_deref().unwrap_or("unknown"),
                        release.tracks.len(),
                        release.audio_resources.len(),
                        release.image_resources.len()
                    )?;
                }

                release
            }
            Err(e) => {
                error!("❌ XML 解析失败: {} - {}", xml_path, e);

                // 标记解析失败
                if let Some(ref mut status_tracker) = self.status_tracker {
                    status_tracker.mark_parsing_failed(&xml_path, &e.to_string()).await?;
                }

                // 记录解析失败
                if let Some(ref mut logger) = self.logger {
                    logger.record_parse_failure(&xml_path, &e.to_string())?;
                }

                return Err(e);
            }
        };

        // 第二步：上传前检查（如果启用）
        if self.config.check_before_upload {
            info!("🔍 步骤2: 检查数据是否已存在（上传前检查模式）");

            let db = self.database.as_ref()
                .ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

            // 检查专辑是否存在（基于 ICPN）
            let album_exists = self.data_comparator.album_exists_by_icpn(db, &parsed_release.album_info).await?;
            if album_exists {
                info!("⏭️  专辑已存在 (ICPN: {:?})，跳过整个处理流程: {}",
                      parsed_release.album_info.upc, parsed_release.album_info.title);
                return Ok(ProcessResult::Skipped);
            }

            // 检查所有曲目是否存在（基于 ISRC）
            let mut all_tracks_exist = true;
            for track in &parsed_release.tracks {
                let track_exists = self.data_comparator.track_exists_by_isrc(db, track).await?;
                if !track_exists {
                    all_tracks_exist = false;
                    break;
                }
            }

            if all_tracks_exist && !parsed_release.tracks.is_empty() {
                info!("⏭️  所有曲目都已存在，跳过整个处理流程");
                return Ok(ProcessResult::Skipped);
            }

            info!("✨ 发现新数据，继续处理流程");
        }

        // 第三步：上传加密音频和图片资源到 NAS
        info!("🚀 步骤{}: 上传加密音频和图片资源到 NAS", if self.config.check_before_upload { 3 } else { 2 });
        let nas_paths = match self.upload_resources(&parsed_release).await {
            Ok(paths) => {
                info!("✅ 资源上传成功");

                // 记录上传成功
                if let Some(ref mut logger) = self.logger {
                    logger.record_stage(&xml_path, "文件上传", true, Some(format!(
                        "上传了 {} 个文件到 NAS", paths.len()
                    )))?;

                    // 记录每日日志
                    logger.record_daily_upload_success(&xml_path, paths.clone())?;
                }

                paths
            }
            Err(e) => {
                error!("❌ 资源上传失败: {}", e);

                // 记录上传失败
                if let Some(ref mut logger) = self.logger {
                    logger.record_upload_failure(&xml_path, &e.to_string())?;
                }

                return Err(e);
            }
        };

        // 第四步：DDEX部分主要属性与 NAS 文件路径上传至 DB
        info!("💾 步骤{}: 上传 DDEX 属性与 NAS 文件路径至数据库", if self.config.check_before_upload { 4 } else { 3 });
        if let Err(e) = self.save_to_database_internal(&parsed_release, &nas_paths).await {
            error!("❌ 数据库保存失败: {}", e);

            // 记录数据库保存失败
            if let Some(ref mut logger) = self.logger {
                logger.record_database_failure(&xml_path, &e.to_string())?;
            }

            return Err(e);
        } else {
            // 记录数据库保存成功
            if let Some(ref mut logger) = self.logger {
                logger.record_stage(&xml_path, "数据库保存", true, Some(format!(
                    "保存了专辑和 {} 个曲目的元数据", parsed_release.tracks.len()
                )))?;

                // 记录每日日志 - 数据库保存成功
                logger.record_daily_database_success(&xml_path)?;
            }
        }

        // 记录成功完成
        if let Some(ref mut logger) = self.logger {
            logger.record_success(&xml_path)?;
            logger.finish_processing(&xml_path)?;
        }

        info!("🎉 目录处理完成: {}", dir);

        Ok(ProcessResult::Success)
    }
    
    /// 解析 XML 文件
    async fn parse_xml_file(&mut self, xml_path: &str, base_dir: &str) -> Result<ParsedRelease> {
        debug!("解析 XML 文件: {}", xml_path);
        
        // 预处理文件
        self.parser.preprocess_file(xml_path).await?;
        
        // 验证 XML 文件
        self.parser.validate_xml(xml_path)?;
        
        // 解析 XML
        let mut parsed_release = self.parser.parse_xml(xml_path, base_dir).await?;
        
        // 后处理结果
        self.parser.postprocess_result(&mut parsed_release).await?;
        
        Ok(parsed_release)
    }
    
    /// 上传资源文件（按照原有 Sony 逻辑）
    async fn upload_resources(&mut self, parsed_release: &ParsedRelease) -> Result<Vec<String>> {
        if self.grpc_client.is_none() {
            warn!("⚠️  gRPC 客户端未初始化，跳过文件上传");
            return Ok(Vec::new());
        }

        let mut nas_paths = Vec::new();
        let client = self.grpc_client.as_mut().unwrap();

        info!("🌐 开始上传文件到 NAS 服务器");

        // 处理音频文件上传（按照原有 Sony 逻辑）
        for audio_resource in &parsed_release.audio_resources {
            info!("🎧 准备上传音频文件: {} (本地: {})",
                  audio_resource.resource_id,
                  audio_resource.local_path);

            // 使用正确的路径生成逻辑，根据加密配置确定扩展名
            let unknown_grid = "unknown".to_string();
            let grid = parsed_release.album_info.grid.as_ref().unwrap_or(&unknown_grid);
            let date = chrono::Utc::now().format("%Y%m%d").to_string();

            // 根据加密配置确定扩展名
            let extension = match self.config.encryption.version.as_str() {
                "legacy" => "bin",
                "v1" => "encrypted",
                _ => "encrypted", // 默认使用新版
            };

            // 生成专辑目录（使用稳定的时间戳）
            let timestamp = Self::generate_stable_timestamp_static(grid);
            let album_dir = format!("{}_{}", grid, timestamp);

            let nas_path = format!("audio/{}/{}/{}/{}.{}",
                                   self.parser.get_provider_name().to_uppercase(),
                                   date,
                                   album_dir,
                                   audio_resource.resource_id,
                                   extension);

            // 检查本地文件是否存在
            if !audio_resource.local_path.is_empty() &&
               std::path::Path::new(&audio_resource.local_path).exists() {
                // 上传本地文件到 NAS
                let uploaded = client.upload_file_resumable(&audio_resource.local_path, &nas_path).await?;
                if uploaded {
                    info!("✅ 音频文件上传成功: {} -> {}", audio_resource.local_path, nas_path);
                } else {
                    info!("⏭️  音频文件已存在，跳过上传: {} -> {}", audio_resource.local_path, nas_path);
                }
            } else {
                return Err(anyhow::anyhow!("本地音频文件不存在: {}", audio_resource.local_path));
            }

            nas_paths.push(nas_path);
        }

        // 处理图片文件上传（按照原有 Sony 逻辑）
        for image_resource in &parsed_release.image_resources {
            info!("🖼️  准备上传图片文件: {} (本地: {})",
                  image_resource.resource_id,
                  image_resource.local_path);

            // 使用原有的 Sony 路径生成逻辑：image/grid/resource_reference.encrypted
            let nas_path = format!("image/{}/{}.encrypted",
                                   parsed_release.album_info.grid.as_ref().unwrap_or(&"unknown".to_string()),
                                   image_resource.resource_id);

            // 检查本地文件是否存在
            if !image_resource.local_path.is_empty() &&
               std::path::Path::new(&image_resource.local_path).exists() {
                // 上传本地文件到 NAS
                let uploaded = client.upload_file_resumable(&image_resource.local_path, &nas_path).await?;
                if uploaded {
                    info!("✅ 图片文件上传成功: {} -> {}", image_resource.local_path, nas_path);
                } else {
                    info!("⏭️  图片文件已存在，跳过上传: {} -> {}", image_resource.local_path, nas_path);
                }
            } else {
                return Err(anyhow::anyhow!("本地图片文件不存在: {}", image_resource.local_path));
            }

            nas_paths.push(nas_path);
        }

        info!("🎯 资源上传完成，共 {} 个文件", nas_paths.len());
        Ok(nas_paths)
    }

    /// 保存到数据库（使用智能对比和更新机制）
    async fn save_to_database_internal(&self, parsed_release: &ParsedRelease, nas_paths: &[String]) -> Result<()> {
        info!("💾 开始智能数据处理...");

        // 显示要保存的数据摘要
        info!("📊 数据摘要:");
        info!("  - 专辑: {}", parsed_release.album_info.title);
        info!("  - 艺术家: {}", parsed_release.album_info.artist_name);
        info!("  - 曲目数: {}", parsed_release.tracks.len());
        info!("  - 音频文件: {}", parsed_release.audio_resources.len());
        info!("  - 图片文件: {}", parsed_release.image_resources.len());
        info!("  - NAS 路径: {} 个文件", nas_paths.len());

        let db = self.database.as_ref()
            .ok_or_else(|| anyhow::anyhow!("Database not initialized"))?;

        // 使用智能对比模式
        self.save_with_smart_comparison(db, parsed_release, nas_paths).await
    }

    /// 智能对比模式保存
    async fn save_with_smart_comparison(&self, db: &Database, parsed_release: &ParsedRelease, nas_paths: &[String]) -> Result<()> {
        let mut stats = UpdateStats::default();

        // 第一步：对比并处理专辑信息
        info!("🔍 步骤1: 对比专辑数据...");
        let album_comparison = self.data_comparator.compare_album(db, &parsed_release.album_info).await?;
        let album_id = self.data_updater.handle_album(db, &parsed_release.album_info, &album_comparison, &mut stats).await?;

        // 第二步：处理艺术家信息
        info!("👨‍🎤 步骤2: 处理艺术家信息...");
        let mut artist_id_map = std::collections::HashMap::new();
        let artist_id = self.insert_or_get_artist(db, &parsed_release.album_info.artist_name).await?;
        artist_id_map.insert(parsed_release.album_info.artist_name.clone(), artist_id);

        // 第三步：对比并处理曲目信息
        info!("🎵 步骤3: 对比曲目数据...");
        for (i, track) in parsed_release.tracks.iter().enumerate() {
            let nas_file_path = nas_paths.get(i).map(|s| s.as_str()).unwrap_or("");

            // 对比曲目数据
            let track_comparison = self.data_comparator.compare_track(db, track).await?;
            let track_id = self.data_updater.handle_track(db, track, &track_comparison, nas_file_path, &mut stats).await?;

            // 确保专辑-曲目关联存在
            self.data_updater.ensure_album_track_link(db, album_id, track_id).await?;

            // 处理曲目艺术家关联
            let track_artist_id = if let Some(&id) = artist_id_map.get(&track.artist_name) {
                id
            } else {
                let id = self.insert_or_get_artist(db, &track.artist_name).await?;
                artist_id_map.insert(track.artist_name.clone(), id);
                id
            };

            // 确保曲目-艺术家关联存在（使用默认角色ID 2）
            self.data_updater.ensure_track_artist_link(db, track_id, track_artist_id, 2).await?;
        }

        // 打印处理统计信息
        self.data_updater.print_stats(&stats);
        info!("💾 智能数据处理完成 - 专辑ID: {}", album_id);

        // TODO: 记录数据库保存成功的每日日志
        // 暂时注释掉，避免借用冲突
        // if let Some(ref mut logger) = self.logger {
        //     logger.record_daily_database_success(&xml_path)?;
        // }

        Ok(())
    }



    /// 插入专辑信息到数据库（如果已存在则返回现有ID）
    /// 适配 other.sql 的 new_album 表结构
    async fn insert_album(&self, db: &Database, album_info: &AlbumInfo) -> Result<i64> {
        // 首先检查专辑是否已存在（使用 album 表和 album_id 字段）
        let query_select = "SELECT album_id FROM album WHERE grid = ?";

        debug!("查询现有专辑: GRid = {}", album_info.grid.as_ref().unwrap_or(&"unknown".to_string()));
        match db.query(query_select, (album_info.grid.as_ref().unwrap_or(&"unknown".to_string()),)).await {
            Ok(rows) => {
                debug!("查询返回 {} 行结果", rows.len());
                if let Some(row) = rows.first() {
                    if let Some(id) = row.get::<i64, _>("album_id") {
                        info!("专辑已存在，使用现有ID: {} (GRid: {}, Title: {})",
                              id, album_info.grid.as_ref().unwrap_or(&"unknown".to_string()), album_info.title);
                        return Ok(id);
                    }
                }
                debug!("专辑不存在，准备插入新专辑");
            }
            Err(e) => {
                warn!("查询现有专辑失败: {}", e);
                // 继续尝试插入
            }
        }

        // 专辑不存在，执行插入（适配 album 表结构）
        let query_insert = r#"
            INSERT INTO album (
                album_name, grid, icpn, releasedate, duration,
                cover_link, active, created_date, isCustom, domain
            ) VALUES (?, ?, ?, ?,
                CASE
                    WHEN ? > 0 THEN SEC_TO_TIME(?)
                    ELSE NULL
                END,
                ?, 1, NOW(), 0, 1)
        "#;

        let grid = album_info.grid.as_ref().unwrap_or(&"unknown".to_string()).clone();
        let duration_seconds = 0i32; // 暂时设为0，后续可以从曲目计算
        let params = (
            &album_info.title,           // album_name
            &grid,                       // grid
            album_info.upc.as_ref(),     // icpn (使用UPC作为ICPN)
            &album_info.release_date,    // releasedate
            &duration_seconds,           // duration 判断条件
            &duration_seconds,           // duration 转换值
            None::<String>,              // cover_link (暂时为空)
        );

        let album_id = db.execute_insert(query_insert, params).await
            .map_err(|e| anyhow::anyhow!("Failed to insert album '{}' (GRid: {}): {}",
                                        album_info.title, album_info.grid.as_ref().unwrap_or(&"unknown".to_string()), e))?;

        info!("成功插入新专辑: {} (ID: {}, GRid: {})",
              album_info.title, album_id, album_info.grid.as_ref().unwrap_or(&"unknown".to_string()));
        Ok(album_id)
    }

    /// 插入或获取艺术家信息（适配 artist 表结构）
    async fn insert_or_get_artist(&self, db: &Database, artist_name: &str) -> Result<i64> {
        // 首先尝试根据名称查找现有艺术家（使用 artist 表）
        let query_select = "SELECT artist_id FROM artist WHERE artist1 = ?";

        if let Ok(rows) = db.query(query_select, (artist_name,)).await {
            if let Some(row) = rows.first() {
                if let Some(id) = row.get::<i64, _>("artist_id") {
                    debug!("找到现有艺术家: {} (ID: {})", artist_name, id);
                    return Ok(id);
                }
            }
        }

        // 如果不存在，插入新艺术家（适配 artist 表结构）
        let query_insert = r#"
            INSERT INTO artist (artist1, usagecount)
            VALUES (?, 0)
        "#;

        let artist_id = db.execute_insert(query_insert, (artist_name,)).await
            .map_err(|e| anyhow::anyhow!("Failed to insert artist: {}", e))?;

        debug!("插入新艺术家: {} (ID: {})", artist_name, artist_id);

        // 插入艺术家名称到 artist_name 表
        self.insert_artist_name(db, artist_id, artist_name).await?;

        Ok(artist_id)
    }

    /// 插入艺术家名称到 artist_name 表（适配新结构）
    async fn insert_artist_name(&self, db: &Database, artist_id: i64, artist_name: &str) -> Result<()> {
        let query = r#"
            INSERT INTO artist_name (artist_id, artist_name, language)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE artist_name = VALUES(artist_name)
        "#;

        let params = (
            artist_id,
            artist_name,
            None::<String>, // language
        );

        db.execute(query, params).await
            .map_err(|e| anyhow::anyhow!("Failed to insert artist name: {}", e))?;

        debug!("插入艺术家名称: {} (artist_id: {})", artist_name, artist_id);
        Ok(())
    }

    /// 插入曲目信息到数据库（如果已存在则返回现有ID）
    /// 适配 other.sql 的 new_track 表结构
    async fn insert_track(&self, db: &Database, track: &TrackInfo, _album_id: i64, nas_file_path: &str) -> Result<i64> {
        // 先检查 ISRC 是否已存在（使用 track 表和 track_id 字段）
        if let Some(ref isrc) = track.isrc {
            if !isrc.is_empty() {
                let query_select = "SELECT track_id FROM track WHERE isrc = ?";

                match db.query(query_select, (isrc,)).await {
                    Ok(rows) => {
                        if let Some(row) = rows.first() {
                            if let Some(id) = row.get::<i64, _>("track_id") {
                                info!("曲目已存在，使用现有ID: {} (ISRC: {}, Title: {})",
                                      id, isrc, track.title);
                                return Ok(id);
                            }
                        }
                    }
                    Err(e) => {
                        warn!("查询现有曲目失败: {}", e);
                        // 继续尝试插入
                    }
                }
            }
        }

        // 曲目不存在，执行插入（适配 track 表结构）
        let query_insert = r#"
            INSERT INTO track (
                isrc, trackname, duration, filelocation, md5,
                discno, trackno, releasetype, active, created_date,
                isCustom, domain
            ) VALUES (?, ?,
                CASE
                    WHEN ? > 0 THEN SEC_TO_TIME(?)
                    ELSE NULL
                END,
                ?, ?, 1, ?, 'Single', 1, NOW(), 0, 1)
        "#;

        let duration_seconds = track.duration_seconds as i32;
        let params = (
            track.isrc.as_ref(),                                                    // isrc
            &track.title,                                                          // trackname
            &duration_seconds,                                                     // duration 判断条件
            &duration_seconds,                                                     // duration 转换值
            if nas_file_path.is_empty() { None } else { Some(nas_file_path) },    // filelocation
            None::<String>,                                                        // md5 (暂时为空)
            track.track_number as i32,                                            // trackno
        );

        let track_id = db.execute_insert(query_insert, params).await
            .map_err(|e| anyhow::anyhow!("Failed to insert track '{}' (ISRC: {}): {}",
                                        track.title, track.isrc.as_ref().unwrap_or(&"unknown".to_string()), e))?;

        info!("成功插入新曲目: {} (ID: {}, ISRC: {})",
              track.title, track_id, track.isrc.as_ref().unwrap_or(&"unknown".to_string()));
        Ok(track_id)
    }

    /// 插入专辑-曲目关联（使用 m3_album_link 表）
    async fn insert_album_track_link(&self, db: &Database, album_id: i64, track_id: i64) -> Result<()> {
        let query = r#"
            INSERT INTO m3_album_link (track_id, album_id, created_date, created_by)
            VALUES (?, ?, NOW(), 1000010001)
            ON DUPLICATE KEY UPDATE created_date = VALUES(created_date)
        "#;

        let params = (track_id, album_id);

        db.execute(query, params).await
            .map_err(|e| anyhow::anyhow!("Failed to insert album-track link: {}", e))?;

        debug!("关联专辑曲目: album_id={}, track_id={}", album_id, track_id);
        Ok(())
    }

    /// 插入曲目艺术家关联（使用 m3_artist_link 表）
    async fn insert_track_artist(&self, db: &Database, track_id: i64, artist_id: i64) -> Result<()> {
        let query = r#"
            INSERT INTO m3_artist_link (track_id, artist_id, role_id, created_date, created_by)
            VALUES (?, ?, ?, NOW(), 1000010001)
            ON DUPLICATE KEY UPDATE created_date = VALUES(created_date)
        "#;

        let params = (
            track_id,
            artist_id,
            2i32, // 默认角色ID (假设2代表主要艺术家)
        );

        db.execute(query, params).await
            .map_err(|e| anyhow::anyhow!("Failed to insert track artist: {}", e))?;

        debug!("关联曲目艺术家: track_id={}, artist_id={}, role_id=2 (默认)", track_id, artist_id);
        Ok(())
    }

    /// 获取处理记录（从日志记录器获取）
    pub fn get_records(&self) -> HashMap<String, Vec<String>> {
        if let Some(ref logger) = self.logger {
            logger.get_summary_records()
        } else {
            HashMap::new()
        }
    }

    /// 发送邮件报告
    pub fn send_email_report(&self) -> Result<()> {
        if !self.config.need_email {
            return Ok(());
        }

        let subject = format!("{} Music Import Report for {}",
                            self.parser.get_provider_name().to_uppercase(),
                            self.date);

        // TODO: 实现邮件发送逻辑
        info!("发送邮件报告: {}", subject);

        Ok(())
    }
    
    /// 清理资源
    pub fn cleanup(&mut self) {
        info!("🧹 清理导入引擎资源");
        
        // 清理状态跟踪器
        if let Some(mut status_tracker) = self.status_tracker.take() {
            if let Err(e) = status_tracker.cleanup() {
                warn!("清理状态跟踪器失败: {}", e);
            }
        }
        
        // 清理日志记录器
        if let Some(mut logger) = self.logger.take() {
            if let Err(e) = logger.cleanup() {
                warn!("清理日志记录器失败: {}", e);
            }
        }
        
        info!("✅ 导入引擎资源清理完成");
    }
}

impl Drop for ImportEngine {
    fn drop(&mut self) {
        self.cleanup();
    }
}
