//! 数据对比和更新模块
//! 
//! 负责对比XML解析后的数据与数据库中的现有数据，
//! 判断是否需要更新，并执行相应的更新操作

use anyhow::Result;
use log::{info, debug, warn};
use serde::{Deserialize, Serialize};

use crate::utils::db::Database;
use crate::importers::models::{AlbumInfo, TrackInfo};

/// 数据对比结果
#[derive(Debug, Clone, PartialEq)]
pub enum ComparisonResult {
    /// 新记录，需要插入
    New,
    /// 记录已存在且数据相同，无需更新
    Unchanged,
    /// 记录已存在但数据有变化，需要更新
    Changed(Vec<String>), // 包含变化的字段列表
}

/// 专辑对比结果
#[derive(Debug, Clone)]
pub struct AlbumComparison {
    pub album_id: Option<i64>,
    pub result: ComparisonResult,
    pub existing_data: Option<ExistingAlbumData>,
}

/// 曲目对比结果
#[derive(Debug, Clone)]
pub struct TrackComparison {
    pub track_id: Option<i64>,
    pub result: ComparisonResult,
    pub existing_data: Option<ExistingTrackData>,
}

/// 数据库中的现有专辑数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExistingAlbumData {
    pub album_id: i64,
    pub album_name: String,
    pub grid: String,
    pub icpn: Option<String>,
    pub releasedate: Option<String>,
    pub duration: Option<String>, // TIME格式
    pub cover_link: Option<String>,
    pub active: i32,
    pub updated_date: Option<String>,
}

/// 数据库中的现有曲目数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExistingTrackData {
    pub track_id: i64,
    pub trackname: String,
    pub isrc: String,
    pub duration: Option<String>, // TIME格式
    pub filelocation: Option<String>,
    pub md5: Option<String>,
    pub trackno: i32,
    pub discno: i32,
    pub active: i32,
    pub updated_date: Option<String>,
}

/// 数据对比器
pub struct DataComparator {
    /// 是否启用详细对比日志
    pub verbose_logging: bool,
}

impl DataComparator {
    /// 创建新的数据对比器
    pub fn new(verbose_logging: bool) -> Self {
        Self { verbose_logging }
    }

    /// 简化的专辑存在性检查（仅基于 ICPN）
    pub async fn album_exists_by_icpn(&self, db: &Database, album_info: &AlbumInfo) -> Result<bool> {
        // 如果没有 UPC/ICPN，认为不存在
        let icpn = match &album_info.upc {
            Some(upc) if !upc.is_empty() => upc,
            _ => return Ok(false),
        };

        let query = "SELECT COUNT(*) as count FROM album WHERE icpn = ?";

        match db.query(query, (icpn,)).await {
            Ok(rows) => {
                if let Some(row) = rows.first() {
                    let count: i64 = row.get("count").unwrap_or(0);
                    let exists = count > 0;

                    if exists {
                        info!("⏭️  专辑已存在 (ICPN: {}): {}", icpn, album_info.title);
                    } else {
                        info!("✨ 专辑不存在 (ICPN: {}): {}", icpn, album_info.title);
                    }

                    Ok(exists)
                } else {
                    Ok(false)
                }
            }
            Err(e) => {
                warn!("查询专辑存在性失败: {}", e);
                Ok(false) // 查询失败时假设不存在
            }
        }
    }

    /// 简化的曲目存在性检查（仅基于 ISRC）
    pub async fn track_exists_by_isrc(&self, db: &Database, track_info: &TrackInfo) -> Result<bool> {
        // 如果没有 ISRC，认为不存在
        let isrc = match &track_info.isrc {
            Some(isrc) if !isrc.is_empty() => isrc,
            _ => return Ok(false),
        };

        let query = "SELECT COUNT(*) as count FROM track WHERE isrc = ?";

        match db.query(query, (isrc,)).await {
            Ok(rows) => {
                if let Some(row) = rows.first() {
                    let count: i64 = row.get("count").unwrap_or(0);
                    let exists = count > 0;

                    if exists {
                        info!("⏭️  曲目已存在 (ISRC: {}): {}", isrc, track_info.title);
                    } else {
                        info!("✨ 曲目不存在 (ISRC: {}): {}", isrc, track_info.title);
                    }

                    Ok(exists)
                } else {
                    Ok(false)
                }
            }
            Err(e) => {
                warn!("查询曲目存在性失败: {}", e);
                Ok(false) // 查询失败时假设不存在
            }
        }
    }

    /// 对比专辑数据
    pub async fn compare_album(&self, db: &Database, album_info: &AlbumInfo) -> Result<AlbumComparison> {
        let unknown_grid = "unknown".to_string();
        let grid = album_info.grid.as_ref().unwrap_or(&unknown_grid);
        
        // 查询现有专辑数据（将日期转换为字符串）
        let query = r#"
            SELECT album_id, album_name, grid, icpn,
                   DATE_FORMAT(releasedate, '%Y-%m-%d') as releasedate,
                   TIME_FORMAT(duration, '%H:%i:%s') as duration,
                   cover_link, active,
                   DATE_FORMAT(updated_date, '%Y-%m-%d %H:%i:%s') as updated_date
            FROM album
            WHERE grid = ?
        "#;

        match db.query(query, (grid,)).await {
            Ok(rows) => {
                if let Some(row) = rows.first() {
                    // 专辑已存在，构建现有数据
                    let existing = ExistingAlbumData {
                        album_id: row.get::<i64, _>("album_id").unwrap_or(0),
                        album_name: row.get::<String, _>("album_name").unwrap_or_default(),
                        grid: row.get::<String, _>("grid").unwrap_or_default(),
                        icpn: row.get::<Option<String>, _>("icpn").unwrap_or(None),
                        releasedate: row.get::<Option<String>, _>("releasedate").unwrap_or(None),
                        duration: row.get::<Option<String>, _>("duration").unwrap_or(None),
                        cover_link: row.get::<Option<String>, _>("cover_link").unwrap_or(None),
                        active: row.get::<i32, _>("active").unwrap_or(1),
                        updated_date: row.get::<Option<String>, _>("updated_date").unwrap_or(None),
                    };

                    // 对比数据是否有变化
                    let changes = self.compare_album_fields(album_info, &existing);
                    
                    let result = if changes.is_empty() {
                        ComparisonResult::Unchanged
                    } else {
                        ComparisonResult::Changed(changes)
                    };

                    if self.verbose_logging {
                        match &result {
                            ComparisonResult::Unchanged => {
                                debug!("专辑数据无变化: {} (GRid: {})", album_info.title, grid);
                            }
                            ComparisonResult::Changed(fields) => {
                                info!("专辑数据有变化: {} (GRid: {}), 变化字段: {:?}", 
                                      album_info.title, grid, fields);
                            }
                            _ => {}
                        }
                    }

                    Ok(AlbumComparison {
                        album_id: Some(existing.album_id),
                        result,
                        existing_data: Some(existing),
                    })
                } else {
                    // 专辑不存在
                    debug!("专辑不存在，需要新增: {} (GRid: {})", album_info.title, grid);
                    Ok(AlbumComparison {
                        album_id: None,
                        result: ComparisonResult::New,
                        existing_data: None,
                    })
                }
            }
            Err(e) => {
                warn!("查询专辑数据失败: {}", e);
                // 查询失败时假设为新记录
                Ok(AlbumComparison {
                    album_id: None,
                    result: ComparisonResult::New,
                    existing_data: None,
                })
            }
        }
    }

    /// 对比曲目数据
    pub async fn compare_track(&self, db: &Database, track_info: &TrackInfo) -> Result<TrackComparison> {
        if let Some(ref isrc) = track_info.isrc {
            if !isrc.is_empty() {
                // 查询现有曲目数据（将时间和日期转换为字符串）
                let query = r#"
                    SELECT track_id, trackname, isrc,
                           TIME_FORMAT(duration, '%H:%i:%s') as duration,
                           filelocation, md5, trackno, discno, active,
                           DATE_FORMAT(updated_date, '%Y-%m-%d %H:%i:%s') as updated_date
                    FROM track
                    WHERE isrc = ?
                "#;

                match db.query(query, (isrc,)).await {
                    Ok(rows) => {
                        if let Some(row) = rows.first() {
                            // 曲目已存在，构建现有数据
                            let existing = ExistingTrackData {
                                track_id: row.get::<i64, _>("track_id").unwrap_or(0),
                                trackname: row.get::<String, _>("trackname").unwrap_or_default(),
                                isrc: row.get::<String, _>("isrc").unwrap_or_default(),
                                duration: row.get::<Option<String>, _>("duration").unwrap_or(None),
                                filelocation: row.get::<Option<String>, _>("filelocation").unwrap_or(None),
                                md5: row.get::<Option<String>, _>("md5").unwrap_or(None),
                                trackno: row.get::<i32, _>("trackno").unwrap_or(1),
                                discno: row.get::<i32, _>("discno").unwrap_or(1),
                                active: row.get::<i32, _>("active").unwrap_or(1),
                                updated_date: row.get::<Option<String>, _>("updated_date").unwrap_or(None),
                            };

                            // 对比数据是否有变化
                            let changes = self.compare_track_fields(track_info, &existing);
                            
                            let result = if changes.is_empty() {
                                ComparisonResult::Unchanged
                            } else {
                                ComparisonResult::Changed(changes)
                            };

                            if self.verbose_logging {
                                match &result {
                                    ComparisonResult::Unchanged => {
                                        debug!("曲目数据无变化: {} (ISRC: {})", track_info.title, isrc);
                                    }
                                    ComparisonResult::Changed(fields) => {
                                        info!("曲目数据有变化: {} (ISRC: {}), 变化字段: {:?}", 
                                              track_info.title, isrc, fields);
                                    }
                                    _ => {}
                                }
                            }

                            return Ok(TrackComparison {
                                track_id: Some(existing.track_id),
                                result,
                                existing_data: Some(existing),
                            });
                        }
                    }
                    Err(e) => {
                        warn!("查询曲目数据失败: {}", e);
                    }
                }
            }
        }

        // 曲目不存在或查询失败
        debug!("曲目不存在，需要新增: {} (ISRC: {:?})", 
               track_info.title, track_info.isrc);
        Ok(TrackComparison {
            track_id: None,
            result: ComparisonResult::New,
            existing_data: None,
        })
    }

    /// 对比专辑字段
    fn compare_album_fields(&self, new_data: &AlbumInfo, existing: &ExistingAlbumData) -> Vec<String> {
        let mut changes = Vec::new();

        // 对比专辑名称
        if new_data.title != existing.album_name {
            changes.push(format!("album_name: '{}' -> '{}'", existing.album_name, new_data.title));
        }

        // 对比ICPN
        let new_icpn = new_data.upc.as_ref().map(|s| s.as_str());
        let existing_icpn = existing.icpn.as_ref().map(|s| s.as_str());
        if new_icpn != existing_icpn {
            changes.push(format!("icpn: '{:?}' -> '{:?}'", existing_icpn, new_icpn));
        }

        // 对比发行日期
        if new_data.release_date != existing.releasedate.as_ref().map(|s| s.as_str()).unwrap_or("") {
            changes.push(format!("releasedate: '{:?}' -> '{}'", existing.releasedate, new_data.release_date));
        }

        // 注意：duration 字段比较复杂，因为数据库存储的是TIME格式，而新数据可能是秒数
        // 这里暂时跳过duration的对比，可以根据需要实现

        changes
    }

    /// 对比曲目字段
    fn compare_track_fields(&self, new_data: &TrackInfo, existing: &ExistingTrackData) -> Vec<String> {
        let mut changes = Vec::new();

        // 对比曲目名称
        if new_data.title != existing.trackname {
            changes.push(format!("trackname: '{}' -> '{}'", existing.trackname, new_data.title));
        }

        // 对比曲目序号
        if new_data.track_number as i32 != existing.trackno {
            changes.push(format!("trackno: {} -> {}", existing.trackno, new_data.track_number));
        }

        // 对比时长（如果有的话）
        if let Some(ref existing_duration) = existing.duration {
            let new_duration_seconds = new_data.duration_seconds as i32;
            // 将 TIME 格式转换为秒数进行对比
            if let Some(existing_seconds) = self.time_to_seconds(existing_duration) {
                if new_duration_seconds != existing_seconds {
                    changes.push(format!("duration: {}s -> {}s", existing_seconds, new_duration_seconds));
                }
            }
        }

        // 注意：filelocation、md5 等字段的对比需要根据实际数据格式来实现
        // 这里提供基础框架，可以根据需要扩展

        changes
    }

    /// 将 TIME 格式转换为秒数
    fn time_to_seconds(&self, time_str: &str) -> Option<i32> {
        // 解析 HH:MM:SS 格式
        let parts: Vec<&str> = time_str.split(':').collect();
        if parts.len() == 3 {
            if let (Ok(hours), Ok(minutes), Ok(seconds)) = (
                parts[0].parse::<i32>(),
                parts[1].parse::<i32>(),
                parts[2].parse::<i32>()
            ) {
                return Some(hours * 3600 + minutes * 60 + seconds);
            }
        }
        None
    }
}
