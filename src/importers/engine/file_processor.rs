//! 文件处理器
//! 
//! 负责文件和目录的扫描、处理等通用功能

use anyhow::Result;
use log::{info, debug};
use rayon::prelude::*;
use walkdir::WalkDir;
use std::sync::Arc;

use crate::config::Config;
use crate::crypto::{Encryptor, EncryptionFactory};
use crate::utils::PathGenerator;

/// 文件处理器
pub struct FileProcessor {
    config: Config,
    encryptor: Option<Arc<dyn Encryptor>>,
    path_generator: Option<PathGenerator>,
}

impl FileProcessor {
    /// 创建新的文件处理器
    pub fn new(config: Config) -> Self {
        Self {
            config,
            encryptor: None,
            path_generator: None,
        }
    }

    /// 创建带加密功能的文件处理器
    pub fn with_encryption(mut self) -> Result<Self> {
        // 创建加密器
        let encryptor = EncryptionFactory::create_encryptor(&self.config.encryption)?;
        self.encryptor = Some(encryptor);

        // 创建路径生成器
        let path_generator = PathGenerator::from_encryption_version(
            &self.config.encryption.version,
            self.config.root_folder.clone()
        );
        self.path_generator = Some(path_generator);

        Ok(self)
    }

    /// 加密音频文件
    pub async fn encrypt_audio_file(
        &self,
        input_path: &str,
        provider: &str,
        date: &str,
        file_id: &str,
    ) -> Result<String> {
        let encryptor = self.encryptor.as_ref()
            .ok_or_else(|| anyhow::anyhow!("加密器未初始化，请使用 with_encryption() 方法"))?;

        let path_generator = self.path_generator.as_ref()
            .ok_or_else(|| anyhow::anyhow!("路径生成器未初始化"))?;

        // 生成输出路径
        let output_path = path_generator.generate_audio_path(provider, date, file_id)?;

        // 确保目录存在
        path_generator.ensure_directory_exists(&output_path)?;

        // 执行加密
        encryptor.encrypt_file(input_path, &output_path, file_id).await?;

        info!("✅ 音频文件加密完成: {} -> {}", input_path, output_path);
        Ok(output_path)
    }

    /// 解密音频文件
    pub async fn decrypt_audio_file(
        &self,
        encrypted_path: &str,
        output_path: &str,
    ) -> Result<()> {
        let encryptor = self.encryptor.as_ref()
            .ok_or_else(|| anyhow::anyhow!("加密器未初始化，请使用 with_encryption() 方法"))?;

        encryptor.decrypt_file(encrypted_path, output_path).await?;

        info!("✅ 音频文件解密完成: {} -> {}", encrypted_path, output_path);
        Ok(())
    }

    /// 解密音频文件的指定范围
    pub async fn decrypt_audio_range(
        &self,
        encrypted_path: &str,
        start: u64,
        length: u64,
    ) -> Result<Vec<u8>> {
        let encryptor = self.encryptor.as_ref()
            .ok_or_else(|| anyhow::anyhow!("加密器未初始化，请使用 with_encryption() 方法"))?;

        let data = encryptor.decrypt_range(encrypted_path, start, length).await?;

        debug!("✅ 音频范围解密完成: {} bytes from {}", data.len(), encrypted_path);
        Ok(data)
    }
    
    /// 获取根目录下的所有子目录（支持两级目录结构）
    pub fn get_directories(&self) -> Result<Vec<String>> {
        debug!("扫描目录: {}", self.config.root_folder);

        let mut directories = Vec::new();

        // 扫描两级目录结构，例如: data/sony/20240901/N_A10301A0002866291X_20240829191115584
        for entry in WalkDir::new(&self.config.root_folder)
            .min_depth(2)
            .max_depth(2) {
            let entry = entry?;

            if entry.file_type().is_dir() {
                // 获取相对于根目录的完整路径
                if let Ok(relative_path) = entry.path().strip_prefix(&self.config.root_folder) {
                    if let Some(path_str) = relative_path.to_str() {
                        directories.push(path_str.to_string());
                    }
                }
            }
        }

        info!("找到 {} 个二级目录", directories.len());
        Ok(directories)
    }
    
    /// 并行处理目录列表
    pub fn process_directories_parallel(
        &self,
        directories: Vec<String>,
        thread_count: usize,
    ) -> Result<Vec<String>> {
        info!("使用 {} 个线程并行处理目录", thread_count);
        
        // 创建线程池
        let pool = rayon::ThreadPoolBuilder::new()
            .num_threads(thread_count)
            .build()?;
        
        // 并行处理（这里只是准备工作，实际处理在调用方进行）
        let processed_dirs = pool.install(|| {
            directories.par_iter()
                .map(|dir| {
                    debug!("准备处理目录: {}", dir);
                    dir.clone()
                })
                .collect()
        });
        
        info!("并行处理准备完成");
        Ok(processed_dirs)
    }
    
    /// 检查目录是否存在
    pub fn directory_exists(&self, dir_name: &str) -> bool {
        let full_path = format!("{}/{}", self.config.root_folder, dir_name);
        std::path::Path::new(&full_path).is_dir()
    }
    
    /// 获取目录的完整路径
    pub fn get_full_path(&self, dir_name: &str) -> String {
        format!("{}/{}", self.config.root_folder, dir_name)
    }
    
    /// 扫描目录中的文件
    pub fn scan_directory_files(&self, dir_path: &str) -> Result<Vec<String>> {
        let mut files = Vec::new();
        
        for entry in WalkDir::new(dir_path) {
            let entry = entry?;
            
            if entry.file_type().is_file() {
                if let Some(file_path) = entry.path().to_str() {
                    files.push(file_path.to_string());
                }
            }
        }
        
        Ok(files)
    }
    
    /// 按扩展名过滤文件
    pub fn filter_files_by_extension(&self, files: Vec<String>, extensions: &[String]) -> Vec<String> {
        files.into_iter()
            .filter(|file| {
                if let Some(ext) = std::path::Path::new(file)
                    .extension()
                    .and_then(|e| e.to_str()) {
                    extensions.iter().any(|supported| supported.eq_ignore_ascii_case(ext))
                } else {
                    false
                }
            })
            .collect()
    }
    
    /// 获取文件大小
    pub fn get_file_size(&self, file_path: &str) -> Result<u64> {
        let metadata = std::fs::metadata(file_path)?;
        Ok(metadata.len())
    }
    
    /// 检查文件是否可读
    pub fn is_file_readable(&self, file_path: &str) -> bool {
        std::fs::File::open(file_path).is_ok()
    }
    
    /// 创建目录（如果不存在）
    pub fn ensure_directory_exists(&self, dir_path: &str) -> Result<()> {
        if !std::path::Path::new(dir_path).exists() {
            std::fs::create_dir_all(dir_path)?;
            info!("创建目录: {}", dir_path);
        }
        Ok(())
    }
    
    /// 获取目录统计信息
    pub fn get_directory_stats(&self, dir_path: &str) -> Result<DirectoryStats> {
        let mut stats = DirectoryStats::default();
        
        for entry in WalkDir::new(dir_path) {
            let entry = entry?;
            
            if entry.file_type().is_file() {
                stats.file_count += 1;
                
                if let Ok(metadata) = entry.metadata() {
                    stats.total_size += metadata.len();
                }
                
                // 按扩展名分类
                if let Some(ext) = entry.path().extension().and_then(|e| e.to_str()) {
                    let ext_lower = ext.to_lowercase();
                    match ext_lower.as_str() {
                        "mp3" | "flac" | "wav" | "aac" | "m4a" => stats.audio_count += 1,
                        "jpg" | "jpeg" | "png" | "gif" | "bmp" => stats.image_count += 1,
                        "xml" => stats.xml_count += 1,
                        _ => stats.other_count += 1,
                    }
                }
            } else if entry.file_type().is_dir() && entry.depth() > 0 {
                stats.subdirectory_count += 1;
            }
        }
        
        Ok(stats)
    }
}

/// 目录统计信息
#[derive(Debug, Default)]
pub struct DirectoryStats {
    /// 文件总数
    pub file_count: usize,
    /// 音频文件数
    pub audio_count: usize,
    /// 图片文件数
    pub image_count: usize,
    /// XML 文件数
    pub xml_count: usize,
    /// 其他文件数
    pub other_count: usize,
    /// 子目录数
    pub subdirectory_count: usize,
    /// 总大小（字节）
    pub total_size: u64,
}

impl DirectoryStats {
    /// 格式化大小显示
    pub fn format_size(&self) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = self.total_size as f64;
        let mut unit_index = 0;
        
        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }
        
        format!("{:.2} {}", size, UNITS[unit_index])
    }
    
    /// 生成统计报告
    pub fn generate_report(&self) -> String {
        format!(
            "目录统计:\n\
             - 文件总数: {}\n\
             - 音频文件: {}\n\
             - 图片文件: {}\n\
             - XML 文件: {}\n\
             - 其他文件: {}\n\
             - 子目录: {}\n\
             - 总大小: {}",
            self.file_count,
            self.audio_count,
            self.image_count,
            self.xml_count,
            self.other_count,
            self.subdirectory_count,
            self.format_size()
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::Config;
    
    #[test]
    fn test_directory_stats_format_size() {
        let mut stats = DirectoryStats::default();
        
        stats.total_size = 1024;
        assert_eq!(stats.format_size(), "1.00 KB");
        
        stats.total_size = 1024 * 1024;
        assert_eq!(stats.format_size(), "1.00 MB");
        
        stats.total_size = 1536; // 1.5 KB
        assert_eq!(stats.format_size(), "1.50 KB");
    }
    
    #[test]
    fn test_filter_files_by_extension() {
        let config = Config::default_for_testing("test");
        let processor = FileProcessor::new(config);
        
        let files = vec![
            "test.mp3".to_string(),
            "test.txt".to_string(),
            "test.flac".to_string(),
            "test.jpg".to_string(),
        ];
        
        let audio_extensions = vec!["mp3".to_string(), "flac".to_string()];
        let filtered = processor.filter_files_by_extension(files, &audio_extensions);
        
        assert_eq!(filtered.len(), 2);
        assert!(filtered.contains(&"test.mp3".to_string()));
        assert!(filtered.contains(&"test.flac".to_string()));
    }
}
