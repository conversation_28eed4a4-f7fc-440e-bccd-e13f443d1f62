@echo off
chcp 65001 >nul

REM 音乐导入系统 - 客户端测试脚本 (Windows)
REM 使用方法: test_client.bat [provider]
REM 示例: test_client.bat sony

echo 🎵 音乐导入系统 - 客户端测试脚本
echo ==================================

REM 获取提供商参数
set PROVIDER=%1
if "%PROVIDER%"=="" set PROVIDER=sony

REM 检查二进制文件是否存在
if not exist "..\target\release\music_import.exe" (
    echo ❌ 错误: 找不到编译后的二进制文件
    echo 请先运行: cargo build --release
    pause
    exit /b 1
)

REM 检查配置文件是否存在
set CONFIG_FILE=..\config\%PROVIDER%.json
if not exist "%CONFIG_FILE%" (
    echo ❌ 错误: 找不到配置文件 %CONFIG_FILE%
    echo 支持的提供商: sony, warner, umg
    pause
    exit /b 1
)

REM 检查数据目录
set DATA_DIR=..\data\%PROVIDER%
if not exist "%DATA_DIR%" (
    echo ❌ 错误: 找不到数据目录 %DATA_DIR%
    echo 请确保数据目录存在并包含 DDEX XML 文件
    pause
    exit /b 1
)

echo 🎯 测试配置:
echo 提供商: %PROVIDER%
echo 配置文件: %CONFIG_FILE%
echo 数据目录: %DATA_DIR%
echo.

REM 检查 gRPC 服务器连接
echo 🔍 检查 gRPC 服务器连接...
netstat -an | find "50051" >nul
if errorlevel 1 (
    echo ⚠️  警告: gRPC 服务器 (localhost:50051) 不可达
    echo 请确保服务器已启动: test_server.bat
    echo.
    echo 继续执行将导致程序退出...
    echo.
)

echo 🚀 启动客户端导入...
echo 按 Ctrl+C 中断导入
echo.

REM 启动客户端
cd ..
set RUST_LOG=info
music_import.exe --mode client --config "%CONFIG_FILE%"

echo.
echo ✅ 客户端执行完成
pause
