@echo off
echo ========================================
echo    音乐导入工具 - 客户端启动脚本
echo ========================================
echo.

REM 设置主密钥环境变量 (V1 加密必需)
set AUDIO_MASTER_KEY=26623413dfda3e35e41372cf39978d7f34bb1c3b6bf4013dc1eb9a7a0fd4c367

echo 🔑 已设置 AUDIO_MASTER_KEY 环境变量
echo 📁 请确保 data/sony 目录下有两级子目录结构，例如:
echo    data/sony/20240901/N_A10301A0002866291X_20240829191115584/
echo 🚀 启动 Sony 客户端...
echo.

REM 启动客户端
music_import.exe --mode client --config config/sony.json

echo.
echo 客户端处理完成
pause
