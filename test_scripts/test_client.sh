#!/bin/bash

# 音乐导入系统 - 客户端测试脚本
# 使用方法: ./test_client.sh [provider]
# 示例: ./test_client.sh sony

echo "🎵 音乐导入系统 - 客户端测试脚本"
echo "=================================="

# 获取提供商参数
PROVIDER=${1:-sony}

# 检查二进制文件是否存在
if [ ! -f "../target/release/music_import" ]; then
    echo "❌ 错误: 找不到编译后的二进制文件"
    echo "请先运行: cargo build --release"
    exit 1
fi

# 检查配置文件是否存在
CONFIG_FILE="../config/${PROVIDER}.json"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 错误: 找不到配置文件 $CONFIG_FILE"
    echo "支持的提供商: sony, warner, umg"
    exit 1
fi

# 检查数据目录
DATA_DIR="../data/${PROVIDER}"
if [ ! -d "$DATA_DIR" ]; then
    echo "❌ 错误: 找不到数据目录 $DATA_DIR"
    echo "请确保数据目录存在并包含 DDEX XML 文件"
    exit 1
fi

echo "🎯 测试配置:"
echo "提供商: $PROVIDER"
echo "配置文件: $CONFIG_FILE"
echo "数据目录: $DATA_DIR"
echo ""

# 检查 gRPC 服务器连接
echo "🔍 检查 gRPC 服务器连接..."
if ! nc -z localhost 50051 2>/dev/null; then
    echo "⚠️  警告: gRPC 服务器 (localhost:50051) 不可达"
    echo "请确保服务器已启动: ./test_server.sh"
    echo ""
    echo "继续执行将导致程序退出..."
    echo ""
fi

echo "🚀 启动客户端导入..."
echo "按 Ctrl+C 中断导入"
echo ""

# 启动客户端
cd ..
RUST_LOG=info ./target/release/music_import --mode client --config "$CONFIG_FILE"

echo ""
echo "✅ 客户端执行完成"
