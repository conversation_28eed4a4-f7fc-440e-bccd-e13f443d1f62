@echo off
chcp 65001 >nul

REM 音乐导入系统 - 服务端测试脚本 (Windows)
REM 使用方法: test_server.bat

echo 🎵 音乐导入系统 - 服务端启动脚本
echo ==================================

REM 检查二进制文件是否存在
if not exist "..\target\release\music_import.exe" (
    echo ❌ 错误: 找不到编译后的二进制文件
    echo 请先运行: cargo build --release
    pause
    exit /b 1
)

REM 检查配置文件是否存在
if not exist "..\config\server.json" (
    echo ❌ 错误: 找不到服务端配置文件 config\server.json
    pause
    exit /b 1
)

REM 检查 uploads 目录
if not exist "..\uploads" (
    echo 📁 创建 uploads 目录...
    mkdir "..\uploads"
)

echo 🚀 启动 gRPC 服务器...
echo 监听地址: 0.0.0.0:50051
echo 上传目录: .\uploads
echo.
echo 按 Ctrl+C 停止服务器
echo.

REM 启动服务器
cd ..
set RUST_LOG=info
music_import.exe --mode server --config config\server.json

pause
