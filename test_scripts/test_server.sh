#!/bin/bash

# 音乐导入系统 - 服务端测试脚本
# 使用方法: ./test_server.sh

echo "🎵 音乐导入系统 - 服务端启动脚本"
echo "=================================="

# 检查二进制文件是否存在
if [ ! -f "../target/release/music_import" ]; then
    echo "❌ 错误: 找不到编译后的二进制文件"
    echo "请先运行: cargo build --release"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "../config/server.json" ]; then
    echo "❌ 错误: 找不到服务端配置文件 config/server.json"
    exit 1
fi

# 检查 uploads 目录
if [ ! -d "../uploads" ]; then
    echo "📁 创建 uploads 目录..."
    mkdir -p ../uploads
fi

echo "🚀 启动 gRPC 服务器..."
echo "监听地址: 0.0.0.0:50051"
echo "上传目录: ./uploads"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

# 启动服务器
cd ..
RUST_LOG=info ./target/release/music_import --mode server --config config/server.json
