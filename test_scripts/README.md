# 音乐导入系统 - 测试指南

## 🎵 系统概述

这是一个企业级音乐导入系统，支持 Sony、Warner、UMG 三大音乐公司的 DDEX 格式数据导入。

## 📁 文件结构

```
music_import/
├── target/release/music_import    # 编译后的二进制文件
├── config/                        # 配置文件目录
│   ├── server.json               # 服务端配置
│   ├── sony.json                 # Sony 客户端配置
│   ├── warner.json               # Warner 客户端配置
│   └── umg.json                  # UMG 客户端配置
├── data/                         # 数据目录
│   ├── sony/                     # Sony DDEX 数据
│   ├── warner/                   # Warner DDEX 数据
│   └── umg/                      # UMG DDEX 数据
├── uploads/                      # 服务端文件存储目录
├── logs/                         # 日志目录
└── test_scripts/                 # 测试脚本目录
    ├── test_server.sh            # 服务端启动脚本
    ├── test_client.sh            # 客户端测试脚本
    └── README.md                 # 本文档
```

## 🚀 快速开始

### 1. 启动服务端

```bash
cd test_scripts
./test_server.sh
```

服务端将在 `localhost:50051` 启动 gRPC 服务。

### 2. 运行客户端测试

在另一个终端中：

```bash
cd test_scripts
./test_client.sh sony    # 测试 Sony 导入
./test_client.sh warner  # 测试 Warner 导入
./test_client.sh umg     # 测试 UMG 导入
```

## 🔧 系统特性

### ✅ 核心功能
- **DDEX 解析**: 完整支持 DDEX XML 格式解析
- **gRPC 文件传输**: 高性能的文件上传和下载
- **断点续传**: 支持大文件的断点续传
- **并发处理**: 多文件并行上传
- **状态跟踪**: 完整的处理状态记录
- **错误恢复**: 健壮的错误处理和恢复机制

### ✅ 企业级特性
- **强制 gRPC 依赖**: 确保文件传输的可靠性
- **完整的日志记录**: 详细的操作审计跟踪
- **数据库集成**: MySQL 数据库存储元数据
- **配置管理**: 灵活的配置文件管理
- **多提供商支持**: Sony、Warner、UMG

## 📊 测试数据

当前包含的测试数据：
- **Sony**: 专辑 "EL LABERINTO" (12 首曲目)
- **Warner**: 待添加
- **UMG**: 待添加

## ⚠️ 重要说明

1. **gRPC 依赖**: 客户端必须连接到 gRPC 服务器才能运行
2. **数据库配置**: 确保 MySQL 数据库配置正确
3. **文件权限**: 确保 uploads 目录有写入权限
4. **网络连接**: 确保客户端和服务端网络连通

## 🐛 故障排除

### 客户端连接失败
```
Error: Failed to initialize importer: gRPC client initialization failed
```
**解决方案**: 确保服务端已启动并监听在 50051 端口

### 文件上传失败
```
Failed to create directory: Permission denied
```
**解决方案**: 检查 uploads 目录权限

### 数据库连接失败
```
Failed to connect to database
```
**解决方案**: 检查 MySQL 服务状态和配置

## 📈 性能指标

- **文件大小**: 支持 GB 级大文件
- **并发数**: 支持多文件并行处理
- **传输速度**: 本地网络可达 100MB/s+
- **内存使用**: 流式处理，内存占用低

## 🎯 下一步

1. 添加 Warner 和 UMG 测试数据
2. 配置生产环境数据库
3. 实现 Web 监控界面
4. 添加性能监控和报警
