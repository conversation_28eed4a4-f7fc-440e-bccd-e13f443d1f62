name: Build Music Import System

on:
  workflow_dispatch:
  push:
    branches: [ master, main ]

jobs:
  build-windows:
    name: Build Windows
    runs-on: windows-latest
    timeout-minutes: 60

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Install protobuf compiler (Windows)
      run: |
        # 尝试使用 chocolatey 安装
        choco install protoc --no-progress || echo "Chocolatey install failed, trying direct download"

        # 如果 chocolatey 失败，直接下载
        if (-not (Get-Command protoc -ErrorAction SilentlyContinue)) {
          echo "Downloading protoc directly..."
          $url = "https://github.com/protocolbuffers/protobuf/releases/download/v25.1/protoc-25.1-win64.zip"
          Invoke-WebRequest -Uri $url -OutFile protoc.zip
          Expand-Archive protoc.zip -DestinationPath protoc
          echo "$env:GITHUB_WORKSPACE\protoc\bin" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append
        }

        protoc --version

    - name: Cache cargo dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-

    - name: Check compilation
      run: cargo check --verbose

    - name: Build release
      run: cargo build --release --verbose

    - name: Test binary
      run: |
        ./target/release/music_import.exe --help

    - name: Prepare release package
      run: |
        New-Item -ItemType Directory -Path release -Force
        Copy-Item target\release\music_import.exe release\ -Force

        # 复制配置文件（如果存在）
        if (Test-Path config) {
          Copy-Item config release\config\ -Recurse -Force
          Write-Host "✅ Config directory copied"
        } else {
          Write-Host "⚠️ Config directory not found"
        }

        # 复制测试脚本（如果存在）
        if (Test-Path test_scripts) {
          Copy-Item test_scripts release\test_scripts\ -Recurse -Force
          Write-Host "✅ Test scripts copied"
        } else {
          Write-Host "⚠️ Test scripts not found"
        }

        # 复制文档文件
        if (Test-Path README.md) {
          Copy-Item README.md release\ -Force
          Write-Host "✅ README.md copied"
        } elseif (Test-Path readme.md) {
          Copy-Item readme.md release\README.md -Force
          Write-Host "✅ readme.md copied as README.md"
        } else {
          Write-Host "⚠️ README file not found"
        }

        # 复制其他文档
        @("BUILD.md", "COMPILE.md") | ForEach-Object {
          if (Test-Path $_) {
            Copy-Item $_ release\ -Force
            Write-Host "✅ $_ copied"
          } else {
            Write-Host "⚠️ $_ not found"
          }
        }

    - name: Create archive
      run: |
        Compress-Archive -Path release\* -DestinationPath music_import-windows-x64.zip

    - name: Upload Windows artifact
      uses: actions/upload-artifact@v4
      with:
        name: music_import-windows-x64
        path: music_import-windows-x64.zip
        retention-days: 30

  build-linux:
    name: Build Linux
    runs-on: ubuntu-latest
    timeout-minutes: 60

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y pkg-config libssl-dev protobuf-compiler
        protoc --version

    - name: Cache cargo dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-

    - name: Check compilation
      run: cargo check --verbose

    - name: Build release
      run: cargo build --release --verbose

    - name: Test binary
      run: |
        ./target/release/music_import --help

    - name: Prepare release package
      run: |
        echo "📦 Preparing Linux release package..."
        mkdir -p release
        cp target/release/music_import release/
        echo "✅ Binary copied"

        # 复制配置文件
        if [ -d config ]; then
          cp -r config release/
          echo "✅ Config directory copied"
        else
          echo "⚠️ Config directory not found"
        fi

        # 复制测试脚本
        if [ -d test_scripts ]; then
          cp -r test_scripts release/
          echo "✅ Test scripts copied"
        else
          echo "⚠️ Test scripts not found"
        fi

        # 复制 README 文件
        if [ -f README.md ]; then
          cp README.md release/
          echo "✅ README.md copied"
        elif [ -f readme.md ]; then
          cp readme.md release/README.md
          echo "✅ readme.md copied as README.md"
        else
          echo "⚠️ README file not found"
        fi

        # 复制其他文档
        for doc in BUILD.md COMPILE.md; do
          if [ -f "$doc" ]; then
            cp "$doc" release/
            echo "✅ $doc copied"
          else
            echo "⚠️ $doc not found"
          fi
        done

        echo "📋 Release package contents:"
        ls -la release/

    - name: Create archive
      run: |
        cd release
        tar -czf ../music_import-linux-x64.tar.gz *

    - name: Upload Linux artifact
      uses: actions/upload-artifact@v4
      with:
        name: music_import-linux-x64
        path: music_import-linux-x64.tar.gz
        retention-days: 30

  build-macos:
    name: Build macOS
    runs-on: macos-latest
    timeout-minutes: 60

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install Rust
      uses: dtolnay/rust-toolchain@stable

    - name: Install protobuf compiler (macOS)
      run: |
        brew install protobuf
        protoc --version

    - name: Cache cargo dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-

    - name: Check compilation
      run: cargo check --verbose

    - name: Build release
      run: cargo build --release --verbose

    - name: Test binary
      run: |
        ./target/release/music_import --help

    - name: Prepare release package
      run: |
        echo "📦 Preparing macOS release package..."
        mkdir -p release
        cp target/release/music_import release/
        echo "✅ Binary copied"

        # 复制配置文件
        if [ -d config ]; then
          cp -r config release/
          echo "✅ Config directory copied"
        else
          echo "⚠️ Config directory not found"
        fi

        # 复制测试脚本
        if [ -d test_scripts ]; then
          cp -r test_scripts release/
          echo "✅ Test scripts copied"
        else
          echo "⚠️ Test scripts not found"
        fi

        # 复制 README 文件
        if [ -f README.md ]; then
          cp README.md release/
          echo "✅ README.md copied"
        elif [ -f readme.md ]; then
          cp readme.md release/README.md
          echo "✅ readme.md copied as README.md"
        else
          echo "⚠️ README file not found"
        fi

        # 复制其他文档
        for doc in BUILD.md COMPILE.md; do
          if [ -f "$doc" ]; then
            cp "$doc" release/
            echo "✅ $doc copied"
          else
            echo "⚠️ $doc not found"
          fi
        done

        echo "📋 Release package contents:"
        ls -la release/

    - name: Create archive
      run: |
        cd release
        tar -czf ../music_import-macos-x64.tar.gz *

    - name: Upload macOS artifact
      uses: actions/upload-artifact@v4
      with:
        name: music_import-macos-x64
        path: music_import-macos-x64.tar.gz
        retention-days: 30